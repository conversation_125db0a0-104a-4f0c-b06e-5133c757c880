{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_prepare", "_convertInt32Color", "_utils", "_SvgTouchableMixin", "e", "__esModule", "default", "WebShape", "React", "Component", "prepareProps", "props", "elementRef", "createRef", "lastMergedProps", "setNativeProps", "merged", "Object", "assign", "style", "clean", "prepare", "current", "cleanAttribute", "keys", "cleanValue", "partialStyle", "concat", "value", "setAttribute", "convertInt32ColorToRGBA", "payload", "camelCaseToDashed", "constructor", "hasTouchableProperty", "SvgTouchableMixin", "_remeasureMetricsOnActivation", "remeasure", "bind", "render", "tag", "Error", "createElement", "exports"], "sourceRoot": "../../../src", "sources": ["web/WebShape.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAOA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,kBAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,kBAAA,GAAAN,sBAAA,CAAAC,OAAA;AAAyD,SAAAD,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAElD,MAAMG,QAAQ,SAEXC,cAAK,CAACC,SAAS,CAAI;EAGjBC,YAAYA,CAACC,KAAQ,EAAE;IAC/B,OAAOA,KAAK;EACd;EAEAC,UAAU,gBACRJ,cAAK,CAACK,SAAS,CAAa,CAAC;EAE/BC,eAAe,GAAe,CAAC,CAAC;;EAEhC;AACF;AACA;EACEC,cAAcA,CAACJ,KAAmB,EAAE;IAClC,MAAMK,MAAM,GAAGC,MAAM,CAACC,MAAM,CAC1B,CAAC,CAAC,EACF,IAAI,CAACP,KAAK,EACV,IAAI,CAACG,eAAe,EACpBH,KAAK,CAACQ,KACR,CAAC;IACD,IAAI,CAACL,eAAe,GAAGE,MAAM;IAC7B,MAAMI,KAAK,GAAG,IAAAC,gBAAO,EAAC,IAAI,EAAE,IAAI,CAACX,YAAY,CAACM,MAAM,CAAC,CAAC;IACtD,MAAMM,OAAO,GAAG,IAAI,CAACV,UAAU,CAACU,OAAO;IACvC,IAAIA,OAAO,EAAE;MACX,KAAK,MAAMC,cAAc,IAAIN,MAAM,CAACO,IAAI,CAACJ,KAAK,CAAC,EAAE;QAC/C,MAAMK,UAAU,GAAGL,KAAK,CAACG,cAAc,CAAuB;QAC9D,QAAQA,cAAc;UACpB,KAAK,KAAK;UACV,KAAK,UAAU;YACb;UACF,KAAK,OAAO;YACV;YACA,KAAK,MAAMG,YAAY,IAAK,EAAE,CAAeC,MAAM,CACjDP,KAAK,CAACD,KAAK,IAAI,EACjB,CAAC,EAAE;cACDF,MAAM,CAACC,MAAM,CAACI,OAAO,CAACH,KAAK,EAAEO,YAAY,CAAC;YAC5C;YACA;UACF,KAAK,MAAM;YACT,IAAID,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;cAChD,MAAMG,KAAK,GAAGH,UAAiC;cAC/CH,OAAO,CAACO,YAAY,CAClB,MAAM,EACN,IAAAC,0CAAuB,EAACF,KAAK,CAACG,OAAO,CACvC,CAAC;YACH;YACA;UACF,KAAK,QAAQ;YACX,IAAIN,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;cAChD,MAAMG,KAAK,GAAGH,UAAiC;cAC/CH,OAAO,CAACO,YAAY,CAClB,QAAQ,EACR,IAAAC,0CAAuB,EAACF,KAAK,CAACG,OAAO,CACvC,CAAC;YACH;YACA;UACF;YACE;YACA;YACA;YACAT,OAAO,CAACO,YAAY,CAAC,IAAAG,wBAAiB,EAACT,cAAc,CAAC,EAAEE,UAAU,CAAC;YACnE;QACJ;MACF;IACF;EACF;EAeAQ,WAAWA,CAACtB,KAAQ,EAAE;IACpB,KAAK,CAACA,KAAK,CAAC;;IAEZ;IACA,IAAI,IAAAuB,2BAAoB,EAACvB,KAAK,CAAC,EAAE;MAC/B,IAAAwB,0BAAiB,EAAC,IAAI,CAAC;IACzB;IAEA,IAAI,CAACC,6BAA6B,GAAGC,gBAAS,CAACC,IAAI,CAAC,IAAI,CAAC;EAC3D;EAEAC,MAAMA,CAAA,EAAgB;IACpB,IAAI,CAAC,IAAI,CAACC,GAAG,EAAE;MACb,MAAM,IAAIC,KAAK,CACb,2EACF,CAAC;IACH;IACA,IAAI,CAAC3B,eAAe,GAAG,CAAC,CAAC;IACzB,OAAO,IAAA4B,mCAAa,EAClB,IAAI,CAACF,GAAG,EACR,IAAAnB,gBAAO,EAAC,IAAI,EAAE,IAAI,CAACX,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC,CAC7C,CAAC;EACH;AACF;AAACgC,OAAA,CAAApC,QAAA,GAAAA,QAAA", "ignoreList": []}