import React, { useContext } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { truncateText } from "../utils/helpers";
import Animated, { FadeIn, FadeInDown } from "react-native-reanimated";
import { LinearGradient } from "expo-linear-gradient";
import { ShareButton } from "./ShareButton";
import { SaveButton } from "./SaveButton";
import { ThemeContext } from "../context/ThemeContext";

const SCREEN_HEIGHT = Dimensions.get("window").height;

export const Card = ({
  title,
  shortContent,
  imageUrl,
  onPress,
  date,
  authorFirstName,
  authorLastName,
  viewsCount,
  post,
  nextPost,
  fromPreview = false,
}) => {
  const { isDarkMode } = useContext(ThemeContext);
  const authorName =
    [authorFirstName, authorLastName].filter(Boolean).join(" ") || "Anonymous";

  const AnimatedTouchableOpacity =
    Animated.createAnimatedComponent(TouchableOpacity);

  return (
    <View style={styles.container}>
      <AnimatedTouchableOpacity
        style={[styles.card, isDarkMode && styles.cardDark]}
        onPress={onPress}
        activeOpacity={0.9}
        entering={FadeInDown.duration(500).springify()}
      >
        <View style={styles.imageContainer}>
          <Image
            source={
              imageUrl
                ? { uri: imageUrl }
                : require("../assets/placeholder.png")
            }
            style={styles.image}
            resizeMode="cover"
          />
          <Animated.View
            style={styles.imageOverlay}
            entering={FadeIn.duration(300)}
          >
            <LinearGradient
              colors={["transparent", "rgba(0,0,0,0.7)"]}
              style={styles.gradient}
            >
              <View style={styles.actions}>
                <ShareButton
                  post={post}
                  isDarkMode={isDarkMode}
                  style={styles.actionButton}
                />
                <SaveButton
                  post={post}
                  isDarkMode={isDarkMode}
                  style={styles.actionButton}
                />
              </View>
            </LinearGradient>
          </Animated.View>
        </View>

        <Animated.View
          style={[styles.content, isDarkMode && styles.contentDark]}
          entering={FadeIn.delay(200).duration(500)}
        >
          <View style={styles.mainContent}>
            <Text style={[styles.title, isDarkMode && styles.titleDark]}>
              {truncateText(title, 50)}
            </Text>
            {shortContent && (
              <Text
                style={[
                  styles.shortContent,
                  isDarkMode && styles.shortContentDark,
                ]}
                numberOfLines={8}
              >
                {shortContent}
              </Text>
            )}
          </View>

          <View style={styles.bottomSection}>
            <View style={[styles.footer, isDarkMode && styles.footerDark]}>
              <View style={styles.authorDateContainer}>
                <Text style={[styles.author, isDarkMode && styles.authorDark]}>
                  {authorName}
                </Text>
                <Text style={[styles.date, isDarkMode && styles.dateDark]}>
                  {date}
                </Text>
              </View>
              <View style={styles.viewsContainer}>
                <Text
                  style={[styles.viewsCount, isDarkMode && styles.viewsCountDark]}
                >
                  {viewsCount || 0} views
                </Text>
              </View>
            </View>

            {nextPost && !fromPreview && (
              <View style={styles.nextPostContainer}>
                <View
                  style={[
                    styles.nextPostPreview,
                    isDarkMode && styles.nextPostPreviewDark,
                  ]}
                >
                  <Text
                    style={[
                      styles.nextPostTitle,
                      isDarkMode && styles.nextPostTitleDark,
                    ]}
                  >
                    {truncateText(nextPost.title, 40)}
                  </Text>
                  <Text
                    style={[
                      styles.nextPostHint,
                      isDarkMode && styles.nextPostHintDark,
                    ]}
                  >
                    Swipe up to continue reading
                  </Text>
                </View>
              </View>
            )}
          </View>
        </Animated.View>
      </AnimatedTouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: SCREEN_HEIGHT,
    width: "100%",
  },
  card: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  cardDark: {
    backgroundColor: "#121212",
  },
  imageContainer: {
    width: "100%",
    height: "30%",
  },
  image: {
    width: "100%",
    height: "100%",
    backgroundColor: "#f0f0f0",
  },
  imageOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gradient: {
    flex: 1,
    justifyContent: "flex-end",
    padding: 15,
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: "space-between",
  },
  contentDark: {
    backgroundColor: "#121212",
  },
  mainContent: {
    flex: 1,
  },
  bottomSection: {
    marginTop: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 23,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 10,
  },
  titleDark: {
    color: "#ffffff",
  },
  shortContent: {
    fontSize: 16,
    color: "#333333",
    lineHeight: 24,
    textAlign: "justify",
  },
  shortContentDark: {
    color: "#e0e0e0",
  },
  footer: {
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: "#eeeeee",
  },
  footerDark: {
    borderTopColor: "#333333",
  },
  authorDateContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  viewsContainer: {
    alignItems: "flex-start",
  },
  author: {
    flex: 1,
    fontSize: 14,
    color: "#666666",
    marginRight: 10,
  },
  authorDark: {
    color: "#a0a0a0",
  },
  viewsCount: {
    fontSize: 14,
    color: "#666666",
  },
  viewsCountDark: {
    color: "#a0a0a0",
  },
  date: {
    fontSize: 14,
    color: "#666666",
  },
  dateDark: {
    color: "#a0a0a0",
  },
  actions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 15,
  },
  nextPostContainer: {
    marginTop: 20,
    marginBottom: 70, // Add bottom margin to account for tab bar
  },
  nextPostPreview: {
    padding: 15,
    backgroundColor: "rgba(255,255,255,0.95)",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  nextPostPreviewDark: {
    backgroundColor: "rgba(26,26,26,0.95)",
  },
  nextPostTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000000",
    marginBottom: 8,
  },
  nextPostTitleDark: {
    color: "#ffffff",
  },
  nextPostHint: {
    fontSize: 12,
    color: "#666666",
    fontStyle: "italic",
  },
  nextPostHintDark: {
    color: "#a0a0a0",
  },
});
