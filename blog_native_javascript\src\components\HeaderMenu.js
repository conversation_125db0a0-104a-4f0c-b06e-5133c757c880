import React, { useState, useCallback } from 'react';
import { View, TouchableOpacity, Modal, Text, StyleSheet } from 'react-native';
import * as Icons from '@expo/vector-icons';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { logout, getStudentProfile } from '../redux/authSlice';

export default function HeaderMenu({ onToggleDarkMode, isDarkMode }) {
  const [menuVisible, setMenuVisible] = useState(false);
  const { isAuthenticated, user } = useSelector(state => state.auth);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const handleLogout = useCallback(() => {
    dispatch(logout());
    setMenuVisible(false);
  }, [dispatch]);

  const handleLogin = useCallback(() => {
    navigation.navigate('Login');
    setMenuVisible(false);
  }, [navigation]);

  const handleSignup = useCallback(() => {
    navigation.navigate('Signup');
    setMenuVisible(false);
  }, [navigation]);

  const handleProfile = useCallback(() => {
    setMenuVisible(false);

    // user object comes from the login response:
    // { user: {...}, id: ..., student_id: ... }
    const studentId = user?.id;
    console.log('Fetching user:', user);
    console.log('Fetching profile for student ID:', studentId);
    if (studentId) {
      dispatch(getStudentProfile({ id: studentId }))
        .unwrap()
        .then(() => {
          navigation.navigate('Home', { screen: 'Profile' });
        })
        .catch(err => {
          console.warn('Failed to load profile:', err);
        });
    } else {
      console.warn('No student info available to fetch profile.');
    }
  }, [dispatch, navigation, user]);

  const handleToggleDarkMode = useCallback(() => {
    onToggleDarkMode();
    setMenuVisible(false);
  }, [onToggleDarkMode]);

  return (
    <View>
      <TouchableOpacity onPress={() => setMenuVisible(true)}>
        <Icons.Ionicons
          name="ellipsis-vertical"
          size={24}
          color={isDarkMode ? '#fff' : '#000'}
        />
      </TouchableOpacity>

      <Modal
        animationType="fade"
        transparent={true}
        visible={menuVisible}
        onRequestClose={() => setMenuVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setMenuVisible(false)}
        >
          <View style={[styles.menuContainer, isDarkMode && styles.menuContainerDark]}>
            {isAuthenticated ? (
              <>
                <TouchableOpacity style={styles.menuItem} onPress={handleProfile}>
                  <Icons.Ionicons
                    name="person-outline"
                    size={20}
                    color={isDarkMode ? '#fff' : '#000'}
                  />
                  <Text style={[styles.menuText, isDarkMode && styles.menuTextDark]}>
                    Profile
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.menuItem} onPress={handleLogout}>
                  <Icons.Ionicons
                    name="log-out-outline"
                    size={20}
                    color={isDarkMode ? '#fff' : '#000'}
                  />
                  <Text style={[styles.menuText, isDarkMode && styles.menuTextDark]}>
                    Logout
                  </Text>
                </TouchableOpacity>
              </>
            ) : (
              <>
                <TouchableOpacity style={styles.menuItem} onPress={handleLogin}>
                  <Icons.Ionicons
                    name="log-in-outline"
                    size={20}
                    color={isDarkMode ? '#fff' : '#000'}
                  />
                  <Text style={[styles.menuText, isDarkMode && styles.menuTextDark]}>
                    Login
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.menuItem} onPress={handleSignup}>
                  <Icons.Ionicons
                    name="person-add-outline"
                    size={20}
                    color={isDarkMode ? '#fff' : '#000'}
                  />
                  <Text style={[styles.menuText, isDarkMode && styles.menuTextDark]}>
                    Sign Up
                  </Text>
                </TouchableOpacity>
              </>
            )}

            <View style={styles.divider} />

            <TouchableOpacity style={styles.menuItem} onPress={handleToggleDarkMode}>
              <Icons.Ionicons
                name={isDarkMode ? 'sunny-outline' : 'moon-outline'}
                size={20}
                color={isDarkMode ? '#fff' : '#000'}
              />
              <Text style={[styles.menuText, isDarkMode && styles.menuTextDark]}>
                {isDarkMode ? 'Light Mode' : 'Dark Mode'}
              </Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  menuContainer: {
    position: 'absolute',
    top: 50,
    right: 20,
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 4,
    minWidth: 150,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  menuContainerDark: {
    backgroundColor: '#333',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  menuText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#000',
  },
  menuTextDark: {
    color: '#fff',
  },
  divider: {
    height: 1,
    backgroundColor: '#ddd',
    marginVertical: 8,
  },
});
