import React, { useContext } from 'react';
import { TouchableOpacity } from 'react-native';
import * as Icons from '@expo/vector-icons';
import { useDispatch, useSelector } from 'react-redux';
import { toggleSavePost } from '../redux/postsSlice';
import { ThemeContext } from '../context/ThemeContext';

export const SaveButton = ({ post, style }) => {
  const dispatch = useDispatch();
  const { isDarkMode } = useContext(ThemeContext);
  const { savedPosts } = useSelector((state) => state.posts);
  const isSaved = Array.isArray(savedPosts) && post?.id && 
    savedPosts.some(savedPost => savedPost?.id === post.id);

  const handleSave = () => {
    if (post?.id) {
      dispatch(toggleSavePost(post));
    }
  };
  return (
    <TouchableOpacity onPress={handleSave} style={style}>
      <Icons.Ionicons        name={isSaved ? "bookmark" : "bookmark-outline"} 
        size={24} 
        color={isDarkMode ? '#ffffff' : '#ebebe0'} 
      />
    </TouchableOpacity>
  );
};
