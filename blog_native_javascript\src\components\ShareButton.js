import React, { useContext } from 'react';
import { TouchableOpacity, Share } from 'react-native';
import * as Icons from '@expo/vector-icons';
import { ThemeContext } from '../context/ThemeContext';

export const ShareButton = ({ post, style }) => {
  const { isDarkMode } = useContext(ThemeContext);

  const handleShare = async () => {
    try {
      const shareMessage = `Check out this article: ${post.title}\n\n${post.short_content || ''}\n\nRead more at: https://blog.shashtrarth.com/blog/${post.slug}`;
      await Share.share({
        message: shareMessage,
        title: post.title,
      });
      
      await Share.open(shareOptions);
    } catch (error) {
      console.log('Error sharing:', error);
    }
  };
  return (
    <TouchableOpacity onPress={handleShare} style={style}>
      <Icons.Ionicons        name="share-outline" 
        size={24} 
        color={isDarkMode ? '#ffffff' : '#ebebe0'} 
      />
    </TouchableOpacity>
  );
};
