import React, { useContext } from 'react';
import { Image } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import * as Icons from '@expo/vector-icons';
import { ThemeContext } from '../context/ThemeContext';

import HomeScreen from '../screens/HomeScreen';
import SavedScreen from '../screens/SavedScreen';
import BlogPostScreen from '../screens/BlogPostScreen';
import ProfileScreen from '../screens/auth/ProfileScreen'; // ✅ make sure path is correct
import HeaderMenu from '../components/HeaderMenu';

const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();

function HomeStack() {
  const { isDarkMode, toggleTheme } = useContext(ThemeContext);
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="HomeScreen" 
        component={HomeScreen}
        options={{
          headerTitle: () => (
            <Image 
              source={require('../assets/placeholder.png')}
              style={{ width: 170, height: 50, marginRight: 10 }}
              resizeMode="contain"
            />
          ),
          headerTitleStyle: {
            fontWeight: 'bold',
            color: isDarkMode ? '#fff' : '#000',
          },
          headerStyle: {
            backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
          },
          headerRight: () => (
            <HeaderMenu onToggleDarkMode={toggleTheme} isDarkMode={isDarkMode} />
          ),
        }}
      />
      <Stack.Screen
        name="BlogPost"
        component={BlogPostScreen}
        options={({ route }) => ({
          title: route.params?.title || 'Blog Post',
          headerTitleStyle: {
            fontWeight: 'bold',
            color: isDarkMode ? '#fff' : '#000',
          },
          headerStyle: {
            backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
          },
          headerTintColor: isDarkMode ? '#fff' : '#000',
          headerRight: () => (
            <HeaderMenu onToggleDarkMode={toggleTheme} isDarkMode={isDarkMode} />
          ),
        })}
      />

      {/* ✅ Add Profile screen here */}
      <Stack.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: 'My Profile',
          headerTitleStyle: {
            fontWeight: 'bold',
            color: isDarkMode ? '#fff' : '#000',
          },
          headerStyle: {
            backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
          },
          headerTintColor: isDarkMode ? '#fff' : '#000',
        }}
      />
    </Stack.Navigator>
  );
}

function SavedStack() {
  const { isDarkMode, toggleTheme } = useContext(ThemeContext);

  return (
    <Stack.Navigator>
      <Stack.Screen
        name="SavedPosts"
        component={SavedScreen}
        options={{
          title: 'Saved Posts',
          headerTitleStyle: {
            fontWeight: 'bold',
            color: isDarkMode ? '#fff' : '#000',
          },
          headerStyle: {
            backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
          },
          headerRight: () => (
            <HeaderMenu onToggleDarkMode={toggleTheme} isDarkMode={isDarkMode} />
          ),
        }}
      />
      <Stack.Screen
        name="BlogPost"
        component={BlogPostScreen}
        options={({ route }) => ({
          title: route.params?.title || 'Blog Post',
          headerTitleStyle: {
            fontWeight: 'bold',
            color: isDarkMode ? '#fff' : '#000',
          },
          headerStyle: {
            backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
          },
          headerTintColor: isDarkMode ? '#fff' : '#000',
          headerRight: () => (
            <HeaderMenu onToggleDarkMode={toggleTheme} isDarkMode={isDarkMode} />
          ),
        })}
      />
    </Stack.Navigator>
  );
}

export default function TabNavigator() {
  const { isDarkMode } = useContext(ThemeContext);
  
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Saved') {
            iconName = focused ? 'bookmark' : 'bookmark-outline';
          }

          return <Icons.Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: isDarkMode ? '#ffffff' : '#000000',
        tabBarInactiveTintColor: isDarkMode ? '#888888' : '#666666',
        tabBarStyle: {
          backgroundColor: isDarkMode ? '#1a1a1a' : '#ffffff',
          borderTopColor: isDarkMode ? '#333333' : '#eeeeee',
        },
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeStack}
        options={{ headerShown: false }}
      />
      <Tab.Screen 
        name="Saved" 
        component={SavedStack}
        options={{ headerShown: false }}
      />
    </Tab.Navigator>
  );
}
