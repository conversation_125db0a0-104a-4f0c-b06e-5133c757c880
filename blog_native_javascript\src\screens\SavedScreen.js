import React, { useContext } from 'react';
import { View, Text, StyleSheet, FlatList, SafeAreaView } from 'react-native';
import { useSelector } from 'react-redux';
import { Card } from '../components/Card';
import { ThemeContext } from '../context/ThemeContext';

export default function SavedScreen({ navigation }) {
  const { savedPosts } = useSelector((state) => state.posts);
  const { isDarkMode } = useContext(ThemeContext);

  const getFullImageUrl = (imageUrl) => {
    if (!imageUrl) return null;
    return imageUrl.startsWith('http')
      ? imageUrl
      : `${process.env.EXPO_PUBLIC_BASE_URL}${imageUrl}`.replace(/([^:]\/)\/+/g, "$1");
  };

  if (!savedPosts || savedPosts.length === 0) {
    return (
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        <View style={[styles.emptyContainer, isDarkMode && styles.emptyContainerDark]}>
          <Text style={[styles.emptyText, isDarkMode && styles.emptyTextDark]}>
            No saved posts yet
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
      <FlatList
        data={savedPosts}
        renderItem={({ item: post }) => (
          <Card
            title={post.title}
            shortContent={post.short_content}
            imageUrl={getFullImageUrl(post.image)}
            date={new Date(post.published_date).toLocaleDateString()}
            authorFirstName={post.author_first_name}
            authorLastName={post.author_last_name}
            viewsCount={post.views_counts}
            onPress={() => navigation.navigate('BlogPost', { slug: post.slug })}
            post={post}
            isDarkMode={isDarkMode}
          />
        )}
        keyExtractor={post => post.id.toString()}
        contentContainerStyle={[styles.listContent, isDarkMode && styles.listContentDark]}
        style={[styles.flatList, isDarkMode && styles.flatListDark]}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#ffffff',
  },
  emptyContainerDark: {
    backgroundColor: '#121212',
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  emptyTextDark: {
    color: '#e0e0e0',
  },
  listContent: {
    paddingBottom: 20,
    backgroundColor: '#ffffff',
  },
  listContentDark: {
    backgroundColor: '#121212',
  },
  flatList: {
    backgroundColor: '#ffffff',
  },
  flatListDark: {
    backgroundColor: '#121212',
  },
});
