(()=>{var __webpack_modules__={6110:(e,t,s)=>{t.formatArgs=formatArgs;t.save=save;t.load=load;t.useColors=useColors;t.storage=localstorage();t.destroy=(()=>{let e=false;return()=>{if(!e){e=true;console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}}})();t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function useColors(){if(typeof window!=="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)){return true}if(typeof navigator!=="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)){return false}let e;return typeof document!=="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!=="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!=="undefined"&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||typeof navigator!=="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function formatArgs(t){t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff);if(!this.useColors){return}const s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0;let n=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{if(e==="%%"){return}r++;if(e==="%c"){n=r}}));t.splice(n,0,s)}t.log=console.debug||console.log||(()=>{});function save(e){try{if(e){t.storage.setItem("debug",e)}else{t.storage.removeItem("debug")}}catch(e){}}function load(){let e;try{e=t.storage.getItem("debug")}catch(e){}if(!e&&typeof process!=="undefined"&&"env"in process){e=process.env.DEBUG}return e}function localstorage(){try{return localStorage}catch(e){}}e.exports=s(897)(t);const{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},897:(e,t,s)=>{function setup(e){createDebug.debug=createDebug;createDebug.default=createDebug;createDebug.coerce=coerce;createDebug.disable=disable;createDebug.enable=enable;createDebug.enabled=enabled;createDebug.humanize=s(744);createDebug.destroy=destroy;Object.keys(e).forEach((t=>{createDebug[t]=e[t]}));createDebug.names=[];createDebug.skips=[];createDebug.formatters={};function selectColor(e){let t=0;for(let s=0;s<e.length;s++){t=(t<<5)-t+e.charCodeAt(s);t|=0}return createDebug.colors[Math.abs(t)%createDebug.colors.length]}createDebug.selectColor=selectColor;function createDebug(e){let t;let s=null;let r;let n;function debug(...e){if(!debug.enabled){return}const s=debug;const r=Number(new Date);const n=r-(t||r);s.diff=n;s.prev=t;s.curr=r;t=r;e[0]=createDebug.coerce(e[0]);if(typeof e[0]!=="string"){e.unshift("%O")}let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((t,r)=>{if(t==="%%"){return"%"}o++;const n=createDebug.formatters[r];if(typeof n==="function"){const r=e[o];t=n.call(s,r);e.splice(o,1);o--}return t}));createDebug.formatArgs.call(s,e);const i=s.log||createDebug.log;i.apply(s,e)}debug.namespace=e;debug.useColors=createDebug.useColors();debug.color=createDebug.selectColor(e);debug.extend=extend;debug.destroy=createDebug.destroy;Object.defineProperty(debug,"enabled",{enumerable:true,configurable:false,get:()=>{if(s!==null){return s}if(r!==createDebug.namespaces){r=createDebug.namespaces;n=createDebug.enabled(e)}return n},set:e=>{s=e}});if(typeof createDebug.init==="function"){createDebug.init(debug)}return debug}function extend(e,t){const s=createDebug(this.namespace+(typeof t==="undefined"?":":t)+e);s.log=this.log;return s}function enable(e){createDebug.save(e);createDebug.namespaces=e;createDebug.names=[];createDebug.skips=[];const t=(typeof e==="string"?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of t){if(e[0]==="-"){createDebug.skips.push(e.slice(1))}else{createDebug.names.push(e)}}}function matchesTemplate(e,t){let s=0;let r=0;let n=-1;let o=0;while(s<e.length){if(r<t.length&&(t[r]===e[s]||t[r]==="*")){if(t[r]==="*"){n=r;o=s;r++}else{s++;r++}}else if(n!==-1){r=n+1;o++;s=o}else{return false}}while(r<t.length&&t[r]==="*"){r++}return r===t.length}function disable(){const e=[...createDebug.names,...createDebug.skips.map((e=>"-"+e))].join(",");createDebug.enable("");return e}function enabled(e){for(const t of createDebug.skips){if(matchesTemplate(e,t)){return false}}for(const t of createDebug.names){if(matchesTemplate(e,t)){return true}}return false}function coerce(e){if(e instanceof Error){return e.stack||e.message}return e}function destroy(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}createDebug.enable(createDebug.load());return createDebug}e.exports=setup},2830:(e,t,s)=>{if(typeof process==="undefined"||process.type==="renderer"||process.browser===true||process.__nwjs){e.exports=s(6110)}else{e.exports=s(5108)}},5108:(e,t,s)=>{const r=s(2018);const n=s(9023);t.init=init;t.log=log;t.formatArgs=formatArgs;t.save=save;t.load=load;t.useColors=useColors;t.destroy=n.deprecate((()=>{}),"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");t.colors=[6,2,3,4,5,1];try{const e=s(1450);if(e&&(e.stderr||e).level>=2){t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221]}}catch(e){}t.inspectOpts=Object.keys(process.env).filter((e=>/^debug_/i.test(e))).reduce(((e,t)=>{const s=t.substring(6).toLowerCase().replace(/_([a-z])/g,((e,t)=>t.toUpperCase()));let r=process.env[t];if(/^(yes|on|true|enabled)$/i.test(r)){r=true}else if(/^(no|off|false|disabled)$/i.test(r)){r=false}else if(r==="null"){r=null}else{r=Number(r)}e[s]=r;return e}),{});function useColors(){return"colors"in t.inspectOpts?Boolean(t.inspectOpts.colors):r.isatty(process.stderr.fd)}function formatArgs(t){const{namespace:s,useColors:r}=this;if(r){const r=this.color;const n="[3"+(r<8?r:"8;5;"+r);const o=`  ${n};1m${s} [0m`;t[0]=o+t[0].split("\n").join("\n"+o);t.push(n+"m+"+e.exports.humanize(this.diff)+"[0m")}else{t[0]=getDate()+s+" "+t[0]}}function getDate(){if(t.inspectOpts.hideDate){return""}return(new Date).toISOString()+" "}function log(...e){return process.stderr.write(n.formatWithOptions(t.inspectOpts,...e)+"\n")}function save(e){if(e){process.env.DEBUG=e}else{delete process.env.DEBUG}}function load(){return process.env.DEBUG}function init(e){e.inspectOpts={};const s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++){e.inspectOpts[s[r]]=t.inspectOpts[s[r]]}}e.exports=s(897)(t);const{formatters:o}=e.exports;o.o=function(e){this.inspectOpts.colors=this.useColors;return n.inspect(e,this.inspectOpts).split("\n").map((e=>e.trim())).join(" ")};o.O=function(e){this.inspectOpts.colors=this.useColors;return n.inspect(e,this.inspectOpts)}},9802:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=s(7075);var n=s(4708);var o=s(7067);var i=s(3136);var a=s(3429);var c=s(7598);var l=s(8522);var u=s(4573);function _interopNamespaceDefault(e){var t=Object.create(null);if(e){Object.keys(e).forEach((function(s){if("default"!==s){var r=Object.getOwnPropertyDescriptor(e,s);Object.defineProperty(t,s,r.get?r:{enumerable:!0,get:function(){return e[s]}})}}))}t.default=e;return t}var f=_interopNamespaceDefault(n);var d=_interopNamespaceDefault(o);var h=_interopNamespaceDefault(i);var p=_interopNamespaceDefault(l);var _=_interopNamespaceDefault(u);var y="\r\n";var b="-".repeat(2);var isBlob=e=>{if("object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&"function"==typeof e.constructor){var t=e[Symbol.toStringTag];return t.startsWith("Blob")||t.startsWith("File")}else{return!1}};var getFormHeader=(e,t,s)=>{var r=`${b}${e}${y}`;r+=`Content-Disposition: form-data; name="${t}"`;if(isBlob(s)){r+=`; filename="${s.name??"blob"}"${y}`;r+=`Content-Type: ${s.type||"application/octet-stream"}`}return`${r}${y}${y}`};var getFormFooter=e=>`${b}${e}${b}${y}${y}`;var g=new TextEncoder;var extractBody=e=>{var t=null;var s;var n=null;if(null==e){s=null;n=0}else if("string"==typeof e){var o=g.encode(`${e}`);t="text/plain;charset=UTF-8";n=o.byteLength;s=o}else if((e=>"object"==typeof e&&"function"==typeof e.append&&"function"==typeof e.delete&&"function"==typeof e.get&&"function"==typeof e.getAll&&"function"==typeof e.has&&"function"==typeof e.set&&"function"==typeof e.sort&&"URLSearchParams"===e[Symbol.toStringTag])(e)){var i=g.encode(e.toString());s=i;n=i.byteLength;t="application/x-www-form-urlencoded;charset=UTF-8"}else if(isBlob(e)){n=e.size;t=e.type||null;s=e.stream()}else if(e instanceof Uint8Array){s=e;n=e.byteLength}else if(a.isAnyArrayBuffer(e)){var l=new Uint8Array(e);s=l;n=l.byteLength}else if(ArrayBuffer.isView(e)){var u=new Uint8Array(e.buffer,e.byteOffset,e.byteLength);s=u;n=u.byteLength}else if((e=>"object"==typeof e&&"function"==typeof e.getReader&&"function"==typeof e.cancel&&"function"==typeof e.tee)(e)){s=e}else if((e=>"object"==typeof e&&"function"==typeof e.append&&"function"==typeof e.set&&"function"==typeof e.get&&"function"==typeof e.getAll&&"function"==typeof e.delete&&"function"==typeof e.keys&&"function"==typeof e.values&&"function"==typeof e.entries&&"function"==typeof e.constructor&&"FormData"===e[Symbol.toStringTag])(e)){var f=`formdata-${c.randomBytes(8).toString("hex")}`;t=`multipart/form-data; boundary=${f}`;n=((e,t)=>{var s=Buffer.byteLength(getFormFooter(t));for(var[r,n]of e){s+=Buffer.byteLength(getFormHeader(t,r,n))+(isBlob(n)?n.size:Buffer.byteLength(`${n}`))+2}return s})(e,f);s=r.Readable.from(async function*generatorOfFormData(e,t){var s=new TextEncoder;for(var[r,n]of e){if(isBlob(n)){yield s.encode(getFormHeader(t,r,n));yield*n.stream();yield s.encode(y)}else{yield s.encode(getFormHeader(t,r,n)+n+y)}}yield s.encode(getFormFooter(t))}(e,f))}else if((e=>"function"==typeof e.getBoundary&&"function"==typeof e.hasKnownLength&&"function"==typeof e.getLengthSync&&r.Readable.isReadable(e))(e)){t=`multipart/form-data; boundary=${e.getBoundary()}`;n=e.hasKnownLength()?e.getLengthSync():null;s=e}else if((e=>r.Readable.isReadable(e))(e)){s=e}else if((e=>"function"==typeof e[Symbol.asyncIterator]||"function"==typeof e[Symbol.iterator])(e)){s=r.Readable.from(e)}else{var d=g.encode(`${e}`);t="text/plain;charset=UTF-8";s=d;n=d.byteLength}return{contentLength:n,contentType:t,body:s}};class InflateStream extends r.Transform{constructor(e){super();this._opts=e}_transform(e,t,s){if(!this._inflate){if(0===e.length){s();return}this._inflate=8==(15&e[0])?p.createInflate(this._opts):p.createInflateRaw(this._opts);this._inflate.on("data",this.push.bind(this));this._inflate.on("end",(()=>this.push(null)));this._inflate.on("error",(e=>this.destroy(e)))}this._inflate.write(e,t,s)}_final(e){if(this._inflate){this._inflate.end();this._inflate=void 0}e()}}var m=globalThis.File||_.File;if(void 0===globalThis.File){globalThis.File=m}var v=Blob;var S=URLSearchParams;var w=URL;var k=Request;var C=Response;var E=Headers;var x=FormData;var headersOfRawHeaders=e=>{var t=new Headers;for(var s=0;s<e.length;s+=2){t.set(e[s],e[s+1])}return t};var methodToHttpOption=e=>{switch(e){case"CONNECT":case"TRACE":case"TRACK":throw new TypeError(`Failed to construct 'Request': '${e}' HTTP method is unsupported.`);default:return e?e.toUpperCase():"GET"}};var urlToHttpOptions=e=>{var t=new w(e);switch(t.protocol){case"http:":case"https:":return h.urlToHttpOptions(t);default:throw new TypeError(`URL scheme "${t.protocol}" is not supported.`)}};async function _fetch(e,t){var s=(e=>null!=e&&"object"==typeof e&&"body"in e)(e);var n=s?e.url:e;var o=s?e.body:t?.body||null;var i=s?e.signal:t?.signal||void 0;var a=(e=>{switch(e){case"follow":case"manual":case"error":return e;case void 0:return"follow";default:throw new TypeError(`Request constructor: ${e} is not an accepted type. Expected one of follow, manual, error.`)}})(s?e.redirect:t?.redirect);var c=new w(n);var l=extractBody(o);var u=0;var h=new Headers(t?.headers||(s?e.headers:void 0));var _={...urlToHttpOptions(c),method:methodToHttpOption(s?e.method:t?.method),signal:i};return await new Promise((function _call(e,t){var s=_.method;var n=("https:"===_.protocol?f:d).request(_);n.on("response",(n=>{n.setTimeout(0);var f={status:n.statusCode,statusText:n.statusMessage,headers:headersOfRawHeaders(n.rawHeaders)};if(301===(b=f.status)||302===b||303===b||307===b||308===b){var d=f.headers.get("Location");var y=null!=d?new w(d,c):null;if("error"===a){t(new Error("URI requested responds with a redirect, redirect mode is set to error"));return}else if("manual"===a&&null!==y){f.headers.set("Location",y.toString())}else if("follow"===a&&null!==y){if(++u>20){t(new Error(`maximum redirect reached at: ${c}`));return}else if("http:"!==y.protocol&&"https:"!==y.protocol){t(new Error("URL scheme must be a HTTP(S) scheme"));return}if(303===f.status||(301===f.status||302===f.status)&&"POST"===s){l=extractBody(null);_.method="GET";h.delete("Content-Length")}else if(null!=l.body&&null==l.contentLength){t(new Error("Cannot follow redirect with a streamed body"));return}else{l=extractBody(o)}Object.assign(_,urlToHttpOptions(c=y));return _call(e,t)}}var b;var destroy=e=>{i?.removeEventListener("abort",destroy);if(e){n.destroy(i?.aborted?i.reason:e);t(i?.aborted?i.reason:e)}};i?.addEventListener("abort",destroy);var g=n;var m=f.headers.get("Content-Encoding")?.toLowerCase();if("HEAD"===s||204===f.status||304===f.status){g=null}else if(null!=m){f.headers.set("Content-Encoding",m);g=r.pipeline(g,(e=>{switch(e){case"br":return p.createBrotliDecompress({flush:p.constants.BROTLI_OPERATION_FLUSH,finishFlush:p.constants.BROTLI_OPERATION_FLUSH});case"gzip":case"x-gzip":return p.createGunzip({flush:p.constants.Z_SYNC_FLUSH,finishFlush:p.constants.Z_SYNC_FLUSH});case"deflate":case"x-deflate":return new InflateStream({flush:p.constants.Z_SYNC_FLUSH,finishFlush:p.constants.Z_SYNC_FLUSH});default:return new r.PassThrough}})(m),destroy)}e(function createResponse(e,t,s){var r=new C(e,t);Object.defineProperty(r,"url",{value:s.url});if("default"!==s.type){Object.defineProperty(r,"type",{value:s.type})}if(s.redirected){Object.defineProperty(r,"redirected",{value:s.redirected})}return r}(g,f,{type:"default",url:c.toString(),redirected:u>0}))}));n.on("error",t);if(!h.has("Accept")){h.set("Accept","*/*")}if(l.contentType){h.set("Content-Type",l.contentType)}if(null==l.body&&("POST"===s||"PUT"===s)){h.set("Content-Length","0")}else if(null!=l.body&&null!=l.contentLength){h.set("Content-Length",`${l.contentLength}`)}((e,t)=>{if("function"==typeof e.setHeaders){e.setHeaders(t)}else{for(var[s,r]of t){e.setHeader(s,r)}}})(n,h);if(null==l.body){n.end()}else if(l.body instanceof Uint8Array){n.write(l.body);n.end()}else{var y=l.body instanceof r.Stream?l.body:r.Readable.fromWeb(l.body);r.pipeline(y,n,(e=>{if(e){t(e)}}))}}))}t.Blob=v;t.File=m;t.FormData=x;t.Headers=E;t.Request=k;t.Response=C;t.URL=w;t.URLSearchParams=S;t["default"]=_fetch;t.fetch=_fetch},3813:e=>{"use strict";e.exports=(e,t=process.argv)=>{const s=e.startsWith("-")?"":e.length===1?"-":"--";const r=t.indexOf(s+e);const n=t.indexOf("--");return r!==-1&&(n===-1||r<n)}},744:e=>{var t=1e3;var s=t*60;var r=s*60;var n=r*24;var o=n*7;var i=n*365.25;e.exports=function(e,t){t=t||{};var s=typeof e;if(s==="string"&&e.length>0){return parse(e)}else if(s==="number"&&isFinite(e)){return t.long?fmtLong(e):fmtShort(e)}throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function parse(e){e=String(e);if(e.length>100){return}var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!a){return}var c=parseFloat(a[1]);var l=(a[2]||"ms").toLowerCase();switch(l){case"years":case"year":case"yrs":case"yr":case"y":return c*i;case"weeks":case"week":case"w":return c*o;case"days":case"day":case"d":return c*n;case"hours":case"hour":case"hrs":case"hr":case"h":return c*r;case"minutes":case"minute":case"mins":case"min":case"m":return c*s;case"seconds":case"second":case"secs":case"sec":case"s":return c*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return undefined}}function fmtShort(e){var o=Math.abs(e);if(o>=n){return Math.round(e/n)+"d"}if(o>=r){return Math.round(e/r)+"h"}if(o>=s){return Math.round(e/s)+"m"}if(o>=t){return Math.round(e/t)+"s"}return e+"ms"}function fmtLong(e){var o=Math.abs(e);if(o>=n){return plural(e,o,n,"day")}if(o>=r){return plural(e,o,r,"hour")}if(o>=s){return plural(e,o,s,"minute")}if(o>=t){return plural(e,o,t,"second")}return e+" ms"}function plural(e,t,s,r){var n=t>=s*1.5;return Math.round(e/s)+" "+r+(n?"s":"")}},1450:(e,t,s)=>{"use strict";const r=s(857);const n=s(2018);const o=s(3813);const{env:i}=process;let a;if(o("no-color")||o("no-colors")||o("color=false")||o("color=never")){a=0}else if(o("color")||o("colors")||o("color=true")||o("color=always")){a=1}if("FORCE_COLOR"in i){if(i.FORCE_COLOR==="true"){a=1}else if(i.FORCE_COLOR==="false"){a=0}else{a=i.FORCE_COLOR.length===0?1:Math.min(parseInt(i.FORCE_COLOR,10),3)}}function translateLevel(e){if(e===0){return false}return{level:e,hasBasic:true,has256:e>=2,has16m:e>=3}}function supportsColor(e,t){if(a===0){return 0}if(o("color=16m")||o("color=full")||o("color=truecolor")){return 3}if(o("color=256")){return 2}if(e&&!t&&a===undefined){return 0}const s=a||0;if(i.TERM==="dumb"){return s}if(process.platform==="win32"){const e=r.release().split(".");if(Number(e[0])>=10&&Number(e[2])>=10586){return Number(e[2])>=14931?3:2}return 1}if("CI"in i){if(["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some((e=>e in i))||i.CI_NAME==="codeship"){return 1}return s}if("TEAMCITY_VERSION"in i){return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(i.TEAMCITY_VERSION)?1:0}if(i.COLORTERM==="truecolor"){return 3}if("TERM_PROGRAM"in i){const e=parseInt((i.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(i.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}if(/-256(color)?$/i.test(i.TERM)){return 2}if(/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(i.TERM)){return 1}if("COLORTERM"in i){return 1}return s}function getSupportLevel(e){const t=supportsColor(e,e&&e.isTTY);return translateLevel(t)}e.exports={supportsColor:getSupportLevel,stdout:translateLevel(supportsColor(true,n.isatty(1))),stderr:translateLevel(supportsColor(true,n.isatty(2)))}},1354:(e,t,s)=>{"use strict";const r=s(6681);r.createWebSocketStream=s(6412);r.Server=s(129);r.Receiver=s(893);r.Sender=s(7389);r.WebSocket=r;r.WebSocketServer=r.Server;e.exports=r},5803:(e,t,s)=>{"use strict";const{EMPTY_BUFFER:r}=s(1791);const n=Buffer[Symbol.species];function concat(e,t){if(e.length===0)return r;if(e.length===1)return e[0];const s=Buffer.allocUnsafe(t);let o=0;for(let t=0;t<e.length;t++){const r=e[t];s.set(r,o);o+=r.length}if(o<t){return new n(s.buffer,s.byteOffset,o)}return s}function _mask(e,t,s,r,n){for(let o=0;o<n;o++){s[r+o]=e[o]^t[o&3]}}function _unmask(e,t){for(let s=0;s<e.length;s++){e[s]^=t[s&3]}}function toArrayBuffer(e){if(e.length===e.buffer.byteLength){return e.buffer}return e.buffer.slice(e.byteOffset,e.byteOffset+e.length)}function toBuffer(e){toBuffer.readOnly=true;if(Buffer.isBuffer(e))return e;let t;if(e instanceof ArrayBuffer){t=new n(e)}else if(ArrayBuffer.isView(e)){t=new n(e.buffer,e.byteOffset,e.byteLength)}else{t=Buffer.from(e);toBuffer.readOnly=false}return t}e.exports={concat:concat,mask:_mask,toArrayBuffer:toArrayBuffer,toBuffer:toBuffer,unmask:_unmask};if(!process.env.WS_NO_BUFFER_UTIL){try{const t=s(8327);e.exports.mask=function(e,s,r,n,o){if(o<48)_mask(e,s,r,n,o);else t.mask(e,s,r,n,o)};e.exports.unmask=function(e,s){if(e.length<32)_unmask(e,s);else t.unmask(e,s)}}catch(e){}}},1791:e=>{"use strict";const t=["nodebuffer","arraybuffer","fragments"];const s=typeof Blob!=="undefined";if(s)t.push("blob");e.exports={BINARY_TYPES:t,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:s,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},4634:(e,t,s)=>{"use strict";const{kForOnEventAttribute:r,kListener:n}=s(1791);const o=Symbol("kCode");const i=Symbol("kData");const a=Symbol("kError");const c=Symbol("kMessage");const l=Symbol("kReason");const u=Symbol("kTarget");const f=Symbol("kType");const d=Symbol("kWasClean");class Event{constructor(e){this[u]=null;this[f]=e}get target(){return this[u]}get type(){return this[f]}}Object.defineProperty(Event.prototype,"target",{enumerable:true});Object.defineProperty(Event.prototype,"type",{enumerable:true});class CloseEvent extends Event{constructor(e,t={}){super(e);this[o]=t.code===undefined?0:t.code;this[l]=t.reason===undefined?"":t.reason;this[d]=t.wasClean===undefined?false:t.wasClean}get code(){return this[o]}get reason(){return this[l]}get wasClean(){return this[d]}}Object.defineProperty(CloseEvent.prototype,"code",{enumerable:true});Object.defineProperty(CloseEvent.prototype,"reason",{enumerable:true});Object.defineProperty(CloseEvent.prototype,"wasClean",{enumerable:true});class ErrorEvent extends Event{constructor(e,t={}){super(e);this[a]=t.error===undefined?null:t.error;this[c]=t.message===undefined?"":t.message}get error(){return this[a]}get message(){return this[c]}}Object.defineProperty(ErrorEvent.prototype,"error",{enumerable:true});Object.defineProperty(ErrorEvent.prototype,"message",{enumerable:true});class MessageEvent extends Event{constructor(e,t={}){super(e);this[i]=t.data===undefined?null:t.data}get data(){return this[i]}}Object.defineProperty(MessageEvent.prototype,"data",{enumerable:true});const h={addEventListener(e,t,s={}){for(const o of this.listeners(e)){if(!s[r]&&o[n]===t&&!o[r]){return}}let o;if(e==="message"){o=function onMessage(e,s){const r=new MessageEvent("message",{data:s?e:e.toString()});r[u]=this;callListener(t,this,r)}}else if(e==="close"){o=function onClose(e,s){const r=new CloseEvent("close",{code:e,reason:s.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});r[u]=this;callListener(t,this,r)}}else if(e==="error"){o=function onError(e){const s=new ErrorEvent("error",{error:e,message:e.message});s[u]=this;callListener(t,this,s)}}else if(e==="open"){o=function onOpen(){const e=new Event("open");e[u]=this;callListener(t,this,e)}}else{return}o[r]=!!s[r];o[n]=t;if(s.once){this.once(e,o)}else{this.on(e,o)}},removeEventListener(e,t){for(const s of this.listeners(e)){if(s[n]===t&&!s[r]){this.removeListener(e,s);break}}}};e.exports={CloseEvent:CloseEvent,ErrorEvent:ErrorEvent,Event:Event,EventTarget:h,MessageEvent:MessageEvent};function callListener(e,t,s){if(typeof e==="object"&&e.handleEvent){e.handleEvent.call(e,s)}else{e.call(t,s)}}},1335:(e,t,s)=>{"use strict";const{tokenChars:r}=s(6615);function push(e,t,s){if(e[t]===undefined)e[t]=[s];else e[t].push(s)}function parse(e){const t=Object.create(null);let s=Object.create(null);let n=false;let o=false;let i=false;let a;let c;let l=-1;let u=-1;let f=-1;let d=0;for(;d<e.length;d++){u=e.charCodeAt(d);if(a===undefined){if(f===-1&&r[u]===1){if(l===-1)l=d}else if(d!==0&&(u===32||u===9)){if(f===-1&&l!==-1)f=d}else if(u===59||u===44){if(l===-1){throw new SyntaxError(`Unexpected character at index ${d}`)}if(f===-1)f=d;const r=e.slice(l,f);if(u===44){push(t,r,s);s=Object.create(null)}else{a=r}l=f=-1}else{throw new SyntaxError(`Unexpected character at index ${d}`)}}else if(c===undefined){if(f===-1&&r[u]===1){if(l===-1)l=d}else if(u===32||u===9){if(f===-1&&l!==-1)f=d}else if(u===59||u===44){if(l===-1){throw new SyntaxError(`Unexpected character at index ${d}`)}if(f===-1)f=d;push(s,e.slice(l,f),true);if(u===44){push(t,a,s);s=Object.create(null);a=undefined}l=f=-1}else if(u===61&&l!==-1&&f===-1){c=e.slice(l,d);l=f=-1}else{throw new SyntaxError(`Unexpected character at index ${d}`)}}else{if(o){if(r[u]!==1){throw new SyntaxError(`Unexpected character at index ${d}`)}if(l===-1)l=d;else if(!n)n=true;o=false}else if(i){if(r[u]===1){if(l===-1)l=d}else if(u===34&&l!==-1){i=false;f=d}else if(u===92){o=true}else{throw new SyntaxError(`Unexpected character at index ${d}`)}}else if(u===34&&e.charCodeAt(d-1)===61){i=true}else if(f===-1&&r[u]===1){if(l===-1)l=d}else if(l!==-1&&(u===32||u===9)){if(f===-1)f=d}else if(u===59||u===44){if(l===-1){throw new SyntaxError(`Unexpected character at index ${d}`)}if(f===-1)f=d;let r=e.slice(l,f);if(n){r=r.replace(/\\/g,"");n=false}push(s,c,r);if(u===44){push(t,a,s);s=Object.create(null);a=undefined}c=undefined;l=f=-1}else{throw new SyntaxError(`Unexpected character at index ${d}`)}}}if(l===-1||i||u===32||u===9){throw new SyntaxError("Unexpected end of input")}if(f===-1)f=d;const h=e.slice(l,f);if(a===undefined){push(t,h,s)}else{if(c===undefined){push(s,h,true)}else if(n){push(s,c,h.replace(/\\/g,""))}else{push(s,c,h)}push(t,a,s)}return t}function format(e){return Object.keys(e).map((t=>{let s=e[t];if(!Array.isArray(s))s=[s];return s.map((e=>[t].concat(Object.keys(e).map((t=>{let s=e[t];if(!Array.isArray(s))s=[s];return s.map((e=>e===true?t:`${t}=${e}`)).join("; ")}))).join("; "))).join(", ")})).join(", ")}e.exports={format:format,parse:parse}},958:e=>{"use strict";const t=Symbol("kDone");const s=Symbol("kRun");class Limiter{constructor(e){this[t]=()=>{this.pending--;this[s]()};this.concurrency=e||Infinity;this.jobs=[];this.pending=0}add(e){this.jobs.push(e);this[s]()}[s](){if(this.pending===this.concurrency)return;if(this.jobs.length){const e=this.jobs.shift();this.pending++;e(this[t])}}}e.exports=Limiter},4376:(e,t,s)=>{"use strict";const r=s(3106);const n=s(5803);const o=s(958);const{kStatusCode:i}=s(1791);const a=Buffer[Symbol.species];const c=Buffer.from([0,0,255,255]);const l=Symbol("permessage-deflate");const u=Symbol("total-length");const f=Symbol("callback");const d=Symbol("buffers");const h=Symbol("error");let p;class PerMessageDeflate{constructor(e,t,s){this._maxPayload=s|0;this._options=e||{};this._threshold=this._options.threshold!==undefined?this._options.threshold:1024;this._isServer=!!t;this._deflate=null;this._inflate=null;this.params=null;if(!p){const e=this._options.concurrencyLimit!==undefined?this._options.concurrencyLimit:10;p=new o(e)}}static get extensionName(){return"permessage-deflate"}offer(){const e={};if(this._options.serverNoContextTakeover){e.server_no_context_takeover=true}if(this._options.clientNoContextTakeover){e.client_no_context_takeover=true}if(this._options.serverMaxWindowBits){e.server_max_window_bits=this._options.serverMaxWindowBits}if(this._options.clientMaxWindowBits){e.client_max_window_bits=this._options.clientMaxWindowBits}else if(this._options.clientMaxWindowBits==null){e.client_max_window_bits=true}return e}accept(e){e=this.normalizeParams(e);this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e);return this.params}cleanup(){if(this._inflate){this._inflate.close();this._inflate=null}if(this._deflate){const e=this._deflate[f];this._deflate.close();this._deflate=null;if(e){e(new Error("The deflate stream was closed while data was being processed"))}}}acceptAsServer(e){const t=this._options;const s=e.find((e=>{if(t.serverNoContextTakeover===false&&e.server_no_context_takeover||e.server_max_window_bits&&(t.serverMaxWindowBits===false||typeof t.serverMaxWindowBits==="number"&&t.serverMaxWindowBits>e.server_max_window_bits)||typeof t.clientMaxWindowBits==="number"&&!e.client_max_window_bits){return false}return true}));if(!s){throw new Error("None of the extension offers can be accepted")}if(t.serverNoContextTakeover){s.server_no_context_takeover=true}if(t.clientNoContextTakeover){s.client_no_context_takeover=true}if(typeof t.serverMaxWindowBits==="number"){s.server_max_window_bits=t.serverMaxWindowBits}if(typeof t.clientMaxWindowBits==="number"){s.client_max_window_bits=t.clientMaxWindowBits}else if(s.client_max_window_bits===true||t.clientMaxWindowBits===false){delete s.client_max_window_bits}return s}acceptAsClient(e){const t=e[0];if(this._options.clientNoContextTakeover===false&&t.client_no_context_takeover){throw new Error('Unexpected parameter "client_no_context_takeover"')}if(!t.client_max_window_bits){if(typeof this._options.clientMaxWindowBits==="number"){t.client_max_window_bits=this._options.clientMaxWindowBits}}else if(this._options.clientMaxWindowBits===false||typeof this._options.clientMaxWindowBits==="number"&&t.client_max_window_bits>this._options.clientMaxWindowBits){throw new Error('Unexpected or invalid parameter "client_max_window_bits"')}return t}normalizeParams(e){e.forEach((e=>{Object.keys(e).forEach((t=>{let s=e[t];if(s.length>1){throw new Error(`Parameter "${t}" must have only a single value`)}s=s[0];if(t==="client_max_window_bits"){if(s!==true){const e=+s;if(!Number.isInteger(e)||e<8||e>15){throw new TypeError(`Invalid value for parameter "${t}": ${s}`)}s=e}else if(!this._isServer){throw new TypeError(`Invalid value for parameter "${t}": ${s}`)}}else if(t==="server_max_window_bits"){const e=+s;if(!Number.isInteger(e)||e<8||e>15){throw new TypeError(`Invalid value for parameter "${t}": ${s}`)}s=e}else if(t==="client_no_context_takeover"||t==="server_no_context_takeover"){if(s!==true){throw new TypeError(`Invalid value for parameter "${t}": ${s}`)}}else{throw new Error(`Unknown parameter "${t}"`)}e[t]=s}))}));return e}decompress(e,t,s){p.add((r=>{this._decompress(e,t,((e,t)=>{r();s(e,t)}))}))}compress(e,t,s){p.add((r=>{this._compress(e,t,((e,t)=>{r();s(e,t)}))}))}_decompress(e,t,s){const o=this._isServer?"client":"server";if(!this._inflate){const e=`${o}_max_window_bits`;const t=typeof this.params[e]!=="number"?r.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=r.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t});this._inflate[l]=this;this._inflate[u]=0;this._inflate[d]=[];this._inflate.on("error",inflateOnError);this._inflate.on("data",inflateOnData)}this._inflate[f]=s;this._inflate.write(e);if(t)this._inflate.write(c);this._inflate.flush((()=>{const e=this._inflate[h];if(e){this._inflate.close();this._inflate=null;s(e);return}const r=n.concat(this._inflate[d],this._inflate[u]);if(this._inflate._readableState.endEmitted){this._inflate.close();this._inflate=null}else{this._inflate[u]=0;this._inflate[d]=[];if(t&&this.params[`${o}_no_context_takeover`]){this._inflate.reset()}}s(null,r)}))}_compress(e,t,s){const o=this._isServer?"server":"client";if(!this._deflate){const e=`${o}_max_window_bits`;const t=typeof this.params[e]!=="number"?r.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=r.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t});this._deflate[u]=0;this._deflate[d]=[];this._deflate.on("data",deflateOnData)}this._deflate[f]=s;this._deflate.write(e);this._deflate.flush(r.Z_SYNC_FLUSH,(()=>{if(!this._deflate){return}let e=n.concat(this._deflate[d],this._deflate[u]);if(t){e=new a(e.buffer,e.byteOffset,e.length-4)}this._deflate[f]=null;this._deflate[u]=0;this._deflate[d]=[];if(t&&this.params[`${o}_no_context_takeover`]){this._deflate.reset()}s(null,e)}))}}e.exports=PerMessageDeflate;function deflateOnData(e){this[d].push(e);this[u]+=e.length}function inflateOnData(e){this[u]+=e.length;if(this[l]._maxPayload<1||this[u]<=this[l]._maxPayload){this[d].push(e);return}this[h]=new RangeError("Max payload size exceeded");this[h].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH";this[h][i]=1009;this.removeListener("data",inflateOnData);this.reset()}function inflateOnError(e){this[l]._inflate=null;e[i]=1007;this[f](e)}},893:(e,t,s)=>{"use strict";const{Writable:r}=s(2203);const n=s(4376);const{BINARY_TYPES:o,EMPTY_BUFFER:i,kStatusCode:a,kWebSocket:c}=s(1791);const{concat:l,toArrayBuffer:u,unmask:f}=s(5803);const{isValidStatusCode:d,isValidUTF8:h}=s(6615);const p=Buffer[Symbol.species];const _=0;const y=1;const b=2;const g=3;const m=4;const v=5;const S=6;class Receiver extends r{constructor(e={}){super();this._allowSynchronousEvents=e.allowSynchronousEvents!==undefined?e.allowSynchronousEvents:true;this._binaryType=e.binaryType||o[0];this._extensions=e.extensions||{};this._isServer=!!e.isServer;this._maxPayload=e.maxPayload|0;this._skipUTF8Validation=!!e.skipUTF8Validation;this[c]=undefined;this._bufferedBytes=0;this._buffers=[];this._compressed=false;this._payloadLength=0;this._mask=undefined;this._fragmented=0;this._masked=false;this._fin=false;this._opcode=0;this._totalPayloadLength=0;this._messageLength=0;this._fragments=[];this._errored=false;this._loop=false;this._state=_}_write(e,t,s){if(this._opcode===8&&this._state==_)return s();this._bufferedBytes+=e.length;this._buffers.push(e);this.startLoop(s)}consume(e){this._bufferedBytes-=e;if(e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){const t=this._buffers[0];this._buffers[0]=new p(t.buffer,t.byteOffset+e,t.length-e);return new p(t.buffer,t.byteOffset,e)}const t=Buffer.allocUnsafe(e);do{const s=this._buffers[0];const r=t.length-e;if(e>=s.length){t.set(this._buffers.shift(),r)}else{t.set(new Uint8Array(s.buffer,s.byteOffset,e),r);this._buffers[0]=new p(s.buffer,s.byteOffset+e,s.length-e)}e-=s.length}while(e>0);return t}startLoop(e){this._loop=true;do{switch(this._state){case _:this.getInfo(e);break;case y:this.getPayloadLength16(e);break;case b:this.getPayloadLength64(e);break;case g:this.getMask();break;case m:this.getData(e);break;case v:case S:this._loop=false;return}}while(this._loop);if(!this._errored)e()}getInfo(e){if(this._bufferedBytes<2){this._loop=false;return}const t=this.consume(2);if((t[0]&48)!==0){const t=this.createError(RangeError,"RSV2 and RSV3 must be clear",true,1002,"WS_ERR_UNEXPECTED_RSV_2_3");e(t);return}const s=(t[0]&64)===64;if(s&&!this._extensions[n.extensionName]){const t=this.createError(RangeError,"RSV1 must be clear",true,1002,"WS_ERR_UNEXPECTED_RSV_1");e(t);return}this._fin=(t[0]&128)===128;this._opcode=t[0]&15;this._payloadLength=t[1]&127;if(this._opcode===0){if(s){const t=this.createError(RangeError,"RSV1 must be clear",true,1002,"WS_ERR_UNEXPECTED_RSV_1");e(t);return}if(!this._fragmented){const t=this.createError(RangeError,"invalid opcode 0",true,1002,"WS_ERR_INVALID_OPCODE");e(t);return}this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented){const t=this.createError(RangeError,`invalid opcode ${this._opcode}`,true,1002,"WS_ERR_INVALID_OPCODE");e(t);return}this._compressed=s}else if(this._opcode>7&&this._opcode<11){if(!this._fin){const t=this.createError(RangeError,"FIN must be set",true,1002,"WS_ERR_EXPECTED_FIN");e(t);return}if(s){const t=this.createError(RangeError,"RSV1 must be clear",true,1002,"WS_ERR_UNEXPECTED_RSV_1");e(t);return}if(this._payloadLength>125||this._opcode===8&&this._payloadLength===1){const t=this.createError(RangeError,`invalid payload length ${this._payloadLength}`,true,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");e(t);return}}else{const t=this.createError(RangeError,`invalid opcode ${this._opcode}`,true,1002,"WS_ERR_INVALID_OPCODE");e(t);return}if(!this._fin&&!this._fragmented)this._fragmented=this._opcode;this._masked=(t[1]&128)===128;if(this._isServer){if(!this._masked){const t=this.createError(RangeError,"MASK must be set",true,1002,"WS_ERR_EXPECTED_MASK");e(t);return}}else if(this._masked){const t=this.createError(RangeError,"MASK must be clear",true,1002,"WS_ERR_UNEXPECTED_MASK");e(t);return}if(this._payloadLength===126)this._state=y;else if(this._payloadLength===127)this._state=b;else this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=false;return}this._payloadLength=this.consume(2).readUInt16BE(0);this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=false;return}const t=this.consume(8);const s=t.readUInt32BE(0);if(s>Math.pow(2,53-32)-1){const t=this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",false,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH");e(t);return}this._payloadLength=s*Math.pow(2,32)+t.readUInt32BE(4);this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8){this._totalPayloadLength+=this._payloadLength;if(this._totalPayloadLength>this._maxPayload&&this._maxPayload>0){const t=this.createError(RangeError,"Max payload size exceeded",false,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");e(t);return}}if(this._masked)this._state=g;else this._state=m}getMask(){if(this._bufferedBytes<4){this._loop=false;return}this._mask=this.consume(4);this._state=m}getData(e){let t=i;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=false;return}t=this.consume(this._payloadLength);if(this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!==0){f(t,this._mask)}}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=v;this.decompress(t,e);return}if(t.length){this._messageLength=this._totalPayloadLength;this._fragments.push(t)}this.dataMessage(e)}decompress(e,t){const s=this._extensions[n.extensionName];s.decompress(e,this._fin,((e,s)=>{if(e)return t(e);if(s.length){this._messageLength+=s.length;if(this._messageLength>this._maxPayload&&this._maxPayload>0){const e=this.createError(RangeError,"Max payload size exceeded",false,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");t(e);return}this._fragments.push(s)}this.dataMessage(t);if(this._state===_)this.startLoop(t)}))}dataMessage(e){if(!this._fin){this._state=_;return}const t=this._messageLength;const s=this._fragments;this._totalPayloadLength=0;this._messageLength=0;this._fragmented=0;this._fragments=[];if(this._opcode===2){let r;if(this._binaryType==="nodebuffer"){r=l(s,t)}else if(this._binaryType==="arraybuffer"){r=u(l(s,t))}else if(this._binaryType==="blob"){r=new Blob(s)}else{r=s}if(this._allowSynchronousEvents){this.emit("message",r,true);this._state=_}else{this._state=S;setImmediate((()=>{this.emit("message",r,true);this._state=_;this.startLoop(e)}))}}else{const r=l(s,t);if(!this._skipUTF8Validation&&!h(r)){const t=this.createError(Error,"invalid UTF-8 sequence",true,1007,"WS_ERR_INVALID_UTF8");e(t);return}if(this._state===v||this._allowSynchronousEvents){this.emit("message",r,false);this._state=_}else{this._state=S;setImmediate((()=>{this.emit("message",r,false);this._state=_;this.startLoop(e)}))}}}controlMessage(e,t){if(this._opcode===8){if(e.length===0){this._loop=false;this.emit("conclude",1005,i);this.end()}else{const s=e.readUInt16BE(0);if(!d(s)){const e=this.createError(RangeError,`invalid status code ${s}`,true,1002,"WS_ERR_INVALID_CLOSE_CODE");t(e);return}const r=new p(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!h(r)){const e=this.createError(Error,"invalid UTF-8 sequence",true,1007,"WS_ERR_INVALID_UTF8");t(e);return}this._loop=false;this.emit("conclude",s,r);this.end()}this._state=_;return}if(this._allowSynchronousEvents){this.emit(this._opcode===9?"ping":"pong",e);this._state=_}else{this._state=S;setImmediate((()=>{this.emit(this._opcode===9?"ping":"pong",e);this._state=_;this.startLoop(t)}))}}createError(e,t,s,r,n){this._loop=false;this._errored=true;const o=new e(s?`Invalid WebSocket frame: ${t}`:t);Error.captureStackTrace(o,this.createError);o.code=n;o[a]=r;return o}}e.exports=Receiver},7389:(e,t,s)=>{"use strict";const{Duplex:r}=s(2203);const{randomFillSync:n}=s(6982);const o=s(4376);const{EMPTY_BUFFER:i,kWebSocket:a,NOOP:c}=s(1791);const{isBlob:l,isValidStatusCode:u}=s(6615);const{mask:f,toBuffer:d}=s(5803);const h=Symbol("kByteLength");const p=Buffer.alloc(4);const _=8*1024;let y;let b=_;const g=0;const m=1;const v=2;class Sender{constructor(e,t,s){this._extensions=t||{};if(s){this._generateMask=s;this._maskBuffer=Buffer.alloc(4)}this._socket=e;this._firstFragment=true;this._compress=false;this._bufferedBytes=0;this._queue=[];this._state=g;this.onerror=c;this[a]=undefined}static frame(e,t){let s;let r=false;let o=2;let i=false;if(t.mask){s=t.maskBuffer||p;if(t.generateMask){t.generateMask(s)}else{if(b===_){if(y===undefined){y=Buffer.alloc(_)}n(y,0,_);b=0}s[0]=y[b++];s[1]=y[b++];s[2]=y[b++];s[3]=y[b++]}i=(s[0]|s[1]|s[2]|s[3])===0;o=6}let a;if(typeof e==="string"){if((!t.mask||i)&&t[h]!==undefined){a=t[h]}else{e=Buffer.from(e);a=e.length}}else{a=e.length;r=t.mask&&t.readOnly&&!i}let c=a;if(a>=65536){o+=8;c=127}else if(a>125){o+=2;c=126}const l=Buffer.allocUnsafe(r?a+o:o);l[0]=t.fin?t.opcode|128:t.opcode;if(t.rsv1)l[0]|=64;l[1]=c;if(c===126){l.writeUInt16BE(a,2)}else if(c===127){l[2]=l[3]=0;l.writeUIntBE(a,4,6)}if(!t.mask)return[l,e];l[1]|=128;l[o-4]=s[0];l[o-3]=s[1];l[o-2]=s[2];l[o-1]=s[3];if(i)return[l,e];if(r){f(e,s,l,o,a);return[l]}f(e,s,e,0,a);return[l,e]}close(e,t,s,r){let n;if(e===undefined){n=i}else if(typeof e!=="number"||!u(e)){throw new TypeError("First argument must be a valid error code number")}else if(t===undefined||!t.length){n=Buffer.allocUnsafe(2);n.writeUInt16BE(e,0)}else{const s=Buffer.byteLength(t);if(s>123){throw new RangeError("The message must not be greater than 123 bytes")}n=Buffer.allocUnsafe(2+s);n.writeUInt16BE(e,0);if(typeof t==="string"){n.write(t,2)}else{n.set(t,2)}}const o={[h]:n.length,fin:true,generateMask:this._generateMask,mask:s,maskBuffer:this._maskBuffer,opcode:8,readOnly:false,rsv1:false};if(this._state!==g){this.enqueue([this.dispatch,n,false,o,r])}else{this.sendFrame(Sender.frame(n,o),r)}}ping(e,t,s){let r;let n;if(typeof e==="string"){r=Buffer.byteLength(e);n=false}else if(l(e)){r=e.size;n=false}else{e=d(e);r=e.length;n=d.readOnly}if(r>125){throw new RangeError("The data size must not be greater than 125 bytes")}const o={[h]:r,fin:true,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:n,rsv1:false};if(l(e)){if(this._state!==g){this.enqueue([this.getBlobData,e,false,o,s])}else{this.getBlobData(e,false,o,s)}}else if(this._state!==g){this.enqueue([this.dispatch,e,false,o,s])}else{this.sendFrame(Sender.frame(e,o),s)}}pong(e,t,s){let r;let n;if(typeof e==="string"){r=Buffer.byteLength(e);n=false}else if(l(e)){r=e.size;n=false}else{e=d(e);r=e.length;n=d.readOnly}if(r>125){throw new RangeError("The data size must not be greater than 125 bytes")}const o={[h]:r,fin:true,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:n,rsv1:false};if(l(e)){if(this._state!==g){this.enqueue([this.getBlobData,e,false,o,s])}else{this.getBlobData(e,false,o,s)}}else if(this._state!==g){this.enqueue([this.dispatch,e,false,o,s])}else{this.sendFrame(Sender.frame(e,o),s)}}send(e,t,s){const r=this._extensions[o.extensionName];let n=t.binary?2:1;let i=t.compress;let a;let c;if(typeof e==="string"){a=Buffer.byteLength(e);c=false}else if(l(e)){a=e.size;c=false}else{e=d(e);a=e.length;c=d.readOnly}if(this._firstFragment){this._firstFragment=false;if(i&&r&&r.params[r._isServer?"server_no_context_takeover":"client_no_context_takeover"]){i=a>=r._threshold}this._compress=i}else{i=false;n=0}if(t.fin)this._firstFragment=true;const u={[h]:a,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:n,readOnly:c,rsv1:i};if(l(e)){if(this._state!==g){this.enqueue([this.getBlobData,e,this._compress,u,s])}else{this.getBlobData(e,this._compress,u,s)}}else if(this._state!==g){this.enqueue([this.dispatch,e,this._compress,u,s])}else{this.dispatch(e,this._compress,u,s)}}getBlobData(e,t,s,r){this._bufferedBytes+=s[h];this._state=v;e.arrayBuffer().then((e=>{if(this._socket.destroyed){const e=new Error("The socket was closed while the blob was being read");process.nextTick(callCallbacks,this,e,r);return}this._bufferedBytes-=s[h];const n=d(e);if(!t){this._state=g;this.sendFrame(Sender.frame(n,s),r);this.dequeue()}else{this.dispatch(n,t,s,r)}})).catch((e=>{process.nextTick(onError,this,e,r)}))}dispatch(e,t,s,r){if(!t){this.sendFrame(Sender.frame(e,s),r);return}const n=this._extensions[o.extensionName];this._bufferedBytes+=s[h];this._state=m;n.compress(e,s.fin,((e,t)=>{if(this._socket.destroyed){const e=new Error("The socket was closed while data was being compressed");callCallbacks(this,e,r);return}this._bufferedBytes-=s[h];this._state=g;s.readOnly=false;this.sendFrame(Sender.frame(t,s),r);this.dequeue()}))}dequeue(){while(this._state===g&&this._queue.length){const e=this._queue.shift();this._bufferedBytes-=e[3][h];Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][h];this._queue.push(e)}sendFrame(e,t){if(e.length===2){this._socket.cork();this._socket.write(e[0]);this._socket.write(e[1],t);this._socket.uncork()}else{this._socket.write(e[0],t)}}}e.exports=Sender;function callCallbacks(e,t,s){if(typeof s==="function")s(t);for(let s=0;s<e._queue.length;s++){const r=e._queue[s];const n=r[r.length-1];if(typeof n==="function")n(t)}}function onError(e,t,s){callCallbacks(e,t,s);e.onerror(t)}},6412:(e,t,s)=>{"use strict";const{Duplex:r}=s(2203);function emitClose(e){e.emit("close")}function duplexOnEnd(){if(!this.destroyed&&this._writableState.finished){this.destroy()}}function duplexOnError(e){this.removeListener("error",duplexOnError);this.destroy();if(this.listenerCount("error")===0){this.emit("error",e)}}function createWebSocketStream(e,t){let s=true;const n=new r({...t,autoDestroy:false,emitClose:false,objectMode:false,writableObjectMode:false});e.on("message",(function message(t,s){const r=!s&&n._readableState.objectMode?t.toString():t;if(!n.push(r))e.pause()}));e.once("error",(function error(e){if(n.destroyed)return;s=false;n.destroy(e)}));e.once("close",(function close(){if(n.destroyed)return;n.push(null)}));n._destroy=function(t,r){if(e.readyState===e.CLOSED){r(t);process.nextTick(emitClose,n);return}let o=false;e.once("error",(function error(e){o=true;r(e)}));e.once("close",(function close(){if(!o)r(t);process.nextTick(emitClose,n)}));if(s)e.terminate()};n._final=function(t){if(e.readyState===e.CONNECTING){e.once("open",(function open(){n._final(t)}));return}if(e._socket===null)return;if(e._socket._writableState.finished){t();if(n._readableState.endEmitted)n.destroy()}else{e._socket.once("finish",(function finish(){t()}));e.close()}};n._read=function(){if(e.isPaused)e.resume()};n._write=function(t,s,r){if(e.readyState===e.CONNECTING){e.once("open",(function open(){n._write(t,s,r)}));return}e.send(t,r)};n.on("end",duplexOnEnd);n.on("error",duplexOnError);return n}e.exports=createWebSocketStream},3332:(e,t,s)=>{"use strict";const{tokenChars:r}=s(6615);function parse(e){const t=new Set;let s=-1;let n=-1;let o=0;for(o;o<e.length;o++){const i=e.charCodeAt(o);if(n===-1&&r[i]===1){if(s===-1)s=o}else if(o!==0&&(i===32||i===9)){if(n===-1&&s!==-1)n=o}else if(i===44){if(s===-1){throw new SyntaxError(`Unexpected character at index ${o}`)}if(n===-1)n=o;const r=e.slice(s,n);if(t.has(r)){throw new SyntaxError(`The "${r}" subprotocol is duplicated`)}t.add(r);s=n=-1}else{throw new SyntaxError(`Unexpected character at index ${o}`)}}if(s===-1||n!==-1){throw new SyntaxError("Unexpected end of input")}const i=e.slice(s,o);if(t.has(i)){throw new SyntaxError(`The "${i}" subprotocol is duplicated`)}t.add(i);return t}e.exports={parse:parse}},6615:(e,t,s)=>{"use strict";const{isUtf8:r}=s(181);const{hasBlob:n}=s(1791);const o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function isValidStatusCode(e){return e>=1e3&&e<=1014&&e!==1004&&e!==1005&&e!==1006||e>=3e3&&e<=4999}function _isValidUTF8(e){const t=e.length;let s=0;while(s<t){if((e[s]&128)===0){s++}else if((e[s]&224)===192){if(s+1===t||(e[s+1]&192)!==128||(e[s]&254)===192){return false}s+=2}else if((e[s]&240)===224){if(s+2>=t||(e[s+1]&192)!==128||(e[s+2]&192)!==128||e[s]===224&&(e[s+1]&224)===128||e[s]===237&&(e[s+1]&224)===160){return false}s+=3}else if((e[s]&248)===240){if(s+3>=t||(e[s+1]&192)!==128||(e[s+2]&192)!==128||(e[s+3]&192)!==128||e[s]===240&&(e[s+1]&240)===128||e[s]===244&&e[s+1]>143||e[s]>244){return false}s+=4}else{return false}}return true}function isBlob(e){return n&&typeof e==="object"&&typeof e.arrayBuffer==="function"&&typeof e.type==="string"&&typeof e.stream==="function"&&(e[Symbol.toStringTag]==="Blob"||e[Symbol.toStringTag]==="File")}e.exports={isBlob:isBlob,isValidStatusCode:isValidStatusCode,isValidUTF8:_isValidUTF8,tokenChars:o};if(r){e.exports.isValidUTF8=function(e){return e.length<24?_isValidUTF8(e):r(e)}}else if(!process.env.WS_NO_UTF_8_VALIDATE){try{const t=s(2414);e.exports.isValidUTF8=function(e){return e.length<32?_isValidUTF8(e):t(e)}}catch(e){}}},129:(e,t,s)=>{"use strict";const r=s(4434);const n=s(8611);const{Duplex:o}=s(2203);const{createHash:i}=s(6982);const a=s(1335);const c=s(4376);const l=s(3332);const u=s(6681);const{GUID:f,kWebSocket:d}=s(1791);const h=/^[+/0-9A-Za-z]{22}==$/;const p=0;const _=1;const y=2;class WebSocketServer extends r{constructor(e,t){super();e={allowSynchronousEvents:true,autoPong:true,maxPayload:100*1024*1024,skipUTF8Validation:false,perMessageDeflate:false,handleProtocols:null,clientTracking:true,verifyClient:null,noServer:false,backlog:null,server:null,host:null,path:null,port:null,WebSocket:u,...e};if(e.port==null&&!e.server&&!e.noServer||e.port!=null&&(e.server||e.noServer)||e.server&&e.noServer){throw new TypeError('One and only one of the "port", "server", or "noServer" options '+"must be specified")}if(e.port!=null){this._server=n.createServer(((e,t)=>{const s=n.STATUS_CODES[426];t.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"});t.end(s)}));this._server.listen(e.port,e.host,e.backlog,t)}else if(e.server){this._server=e.server}if(this._server){const e=this.emit.bind(this,"connection");this._removeListeners=addListeners(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,s,r)=>{this.handleUpgrade(t,s,r,e)}})}if(e.perMessageDeflate===true)e.perMessageDeflate={};if(e.clientTracking){this.clients=new Set;this._shouldEmitClose=false}this.options=e;this._state=p}address(){if(this.options.noServer){throw new Error('The server is operating in "noServer" mode')}if(!this._server)return null;return this._server.address()}close(e){if(this._state===y){if(e){this.once("close",(()=>{e(new Error("The server is not running"))}))}process.nextTick(emitClose,this);return}if(e)this.once("close",e);if(this._state===_)return;this._state=_;if(this.options.noServer||this.options.server){if(this._server){this._removeListeners();this._removeListeners=this._server=null}if(this.clients){if(!this.clients.size){process.nextTick(emitClose,this)}else{this._shouldEmitClose=true}}else{process.nextTick(emitClose,this)}}else{const e=this._server;this._removeListeners();this._removeListeners=this._server=null;e.close((()=>{emitClose(this)}))}}shouldHandle(e){if(this.options.path){const t=e.url.indexOf("?");const s=t!==-1?e.url.slice(0,t):e.url;if(s!==this.options.path)return false}return true}handleUpgrade(e,t,s,r){t.on("error",socketOnError);const n=e.headers["sec-websocket-key"];const o=e.headers.upgrade;const i=+e.headers["sec-websocket-version"];if(e.method!=="GET"){const s="Invalid HTTP method";abortHandshakeOrEmitwsClientError(this,e,t,405,s);return}if(o===undefined||o.toLowerCase()!=="websocket"){const s="Invalid Upgrade header";abortHandshakeOrEmitwsClientError(this,e,t,400,s);return}if(n===undefined||!h.test(n)){const s="Missing or invalid Sec-WebSocket-Key header";abortHandshakeOrEmitwsClientError(this,e,t,400,s);return}if(i!==8&&i!==13){const s="Missing or invalid Sec-WebSocket-Version header";abortHandshakeOrEmitwsClientError(this,e,t,400,s);return}if(!this.shouldHandle(e)){abortHandshake(t,400);return}const u=e.headers["sec-websocket-protocol"];let f=new Set;if(u!==undefined){try{f=l.parse(u)}catch(s){const r="Invalid Sec-WebSocket-Protocol header";abortHandshakeOrEmitwsClientError(this,e,t,400,r);return}}const d=e.headers["sec-websocket-extensions"];const p={};if(this.options.perMessageDeflate&&d!==undefined){const s=new c(this.options.perMessageDeflate,true,this.options.maxPayload);try{const e=a.parse(d);if(e[c.extensionName]){s.accept(e[c.extensionName]);p[c.extensionName]=s}}catch(s){const r="Invalid or unacceptable Sec-WebSocket-Extensions header";abortHandshakeOrEmitwsClientError(this,e,t,400,r);return}}if(this.options.verifyClient){const o={origin:e.headers[`${i===8?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(this.options.verifyClient.length===2){this.options.verifyClient(o,((o,i,a,c)=>{if(!o){return abortHandshake(t,i||401,a,c)}this.completeUpgrade(p,n,f,e,t,s,r)}));return}if(!this.options.verifyClient(o))return abortHandshake(t,401)}this.completeUpgrade(p,n,f,e,t,s,r)}completeUpgrade(e,t,s,r,n,o,l){if(!n.readable||!n.writable)return n.destroy();if(n[d]){throw new Error("server.handleUpgrade() was called more than once with the same "+"socket, possibly due to a misconfiguration")}if(this._state>p)return abortHandshake(n,503);const u=i("sha1").update(t+f).digest("base64");const h=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${u}`];const _=new this.options.WebSocket(null,undefined,this.options);if(s.size){const e=this.options.handleProtocols?this.options.handleProtocols(s,r):s.values().next().value;if(e){h.push(`Sec-WebSocket-Protocol: ${e}`);_._protocol=e}}if(e[c.extensionName]){const t=e[c.extensionName].params;const s=a.format({[c.extensionName]:[t]});h.push(`Sec-WebSocket-Extensions: ${s}`);_._extensions=e}this.emit("headers",h,r);n.write(h.concat("\r\n").join("\r\n"));n.removeListener("error",socketOnError);_.setSocket(n,o,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation});if(this.clients){this.clients.add(_);_.on("close",(()=>{this.clients.delete(_);if(this._shouldEmitClose&&!this.clients.size){process.nextTick(emitClose,this)}}))}l(_,r)}}e.exports=WebSocketServer;function addListeners(e,t){for(const s of Object.keys(t))e.on(s,t[s]);return function removeListeners(){for(const s of Object.keys(t)){e.removeListener(s,t[s])}}}function emitClose(e){e._state=y;e.emit("close")}function socketOnError(){this.destroy()}function abortHandshake(e,t,s,r){s=s||n.STATUS_CODES[t];r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(s),...r};e.once("finish",e.destroy);e.end(`HTTP/1.1 ${t} ${n.STATUS_CODES[t]}\r\n`+Object.keys(r).map((e=>`${e}: ${r[e]}`)).join("\r\n")+"\r\n\r\n"+s)}function abortHandshakeOrEmitwsClientError(e,t,s,r,n){if(e.listenerCount("wsClientError")){const r=new Error(n);Error.captureStackTrace(r,abortHandshakeOrEmitwsClientError);e.emit("wsClientError",r,s,t)}else{abortHandshake(s,r,n)}}},6681:(e,t,s)=>{"use strict";const r=s(4434);const n=s(5692);const o=s(8611);const i=s(9278);const a=s(4756);const{randomBytes:c,createHash:l}=s(6982);const{Duplex:u,Readable:f}=s(2203);const{URL:d}=s(7016);const h=s(4376);const p=s(893);const _=s(7389);const{isBlob:y}=s(6615);const{BINARY_TYPES:b,EMPTY_BUFFER:g,GUID:m,kForOnEventAttribute:v,kListener:S,kStatusCode:w,kWebSocket:k,NOOP:C}=s(1791);const{EventTarget:{addEventListener:E,removeEventListener:x}}=s(4634);const{format:O,parse:T}=s(1335);const{toBuffer:L}=s(5803);const R=30*1e3;const P=Symbol("kAborted");const M=[8,13];const D=["CONNECTING","OPEN","CLOSING","CLOSED"];const B=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class WebSocket extends r{constructor(e,t,s){super();this._binaryType=b[0];this._closeCode=1006;this._closeFrameReceived=false;this._closeFrameSent=false;this._closeMessage=g;this._closeTimer=null;this._errorEmitted=false;this._extensions={};this._paused=false;this._protocol="";this._readyState=WebSocket.CONNECTING;this._receiver=null;this._sender=null;this._socket=null;if(e!==null){this._bufferedAmount=0;this._isServer=false;this._redirects=0;if(t===undefined){t=[]}else if(!Array.isArray(t)){if(typeof t==="object"&&t!==null){s=t;t=[]}else{t=[t]}}initAsClient(this,e,t,s)}else{this._autoPong=s.autoPong;this._isServer=true}}get binaryType(){return this._binaryType}set binaryType(e){if(!b.includes(e))return;this._binaryType=e;if(this._receiver)this._receiver._binaryType=e}get bufferedAmount(){if(!this._socket)return this._bufferedAmount;return this._socket._writableState.length+this._sender._bufferedBytes}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,s){const r=new p({allowSynchronousEvents:s.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:s.maxPayload,skipUTF8Validation:s.skipUTF8Validation});const n=new _(e,this._extensions,s.generateMask);this._receiver=r;this._sender=n;this._socket=e;r[k]=this;n[k]=this;e[k]=this;r.on("conclude",receiverOnConclude);r.on("drain",receiverOnDrain);r.on("error",receiverOnError);r.on("message",receiverOnMessage);r.on("ping",receiverOnPing);r.on("pong",receiverOnPong);n.onerror=senderOnError;if(e.setTimeout)e.setTimeout(0);if(e.setNoDelay)e.setNoDelay();if(t.length>0)e.unshift(t);e.on("close",socketOnClose);e.on("data",socketOnData);e.on("end",socketOnEnd);e.on("error",socketOnError);this._readyState=WebSocket.OPEN;this.emit("open")}emitClose(){if(!this._socket){this._readyState=WebSocket.CLOSED;this.emit("close",this._closeCode,this._closeMessage);return}if(this._extensions[h.extensionName]){this._extensions[h.extensionName].cleanup()}this._receiver.removeAllListeners();this._readyState=WebSocket.CLOSED;this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState===WebSocket.CLOSED)return;if(this.readyState===WebSocket.CONNECTING){const e="WebSocket was closed before the connection was established";abortHandshake(this,this._req,e);return}if(this.readyState===WebSocket.CLOSING){if(this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)){this._socket.end()}return}this._readyState=WebSocket.CLOSING;this._sender.close(e,t,!this._isServer,(e=>{if(e)return;this._closeFrameSent=true;if(this._closeFrameReceived||this._receiver._writableState.errorEmitted){this._socket.end()}}));setCloseTimer(this)}pause(){if(this.readyState===WebSocket.CONNECTING||this.readyState===WebSocket.CLOSED){return}this._paused=true;this._socket.pause()}ping(e,t,s){if(this.readyState===WebSocket.CONNECTING){throw new Error("WebSocket is not open: readyState 0 (CONNECTING)")}if(typeof e==="function"){s=e;e=t=undefined}else if(typeof t==="function"){s=t;t=undefined}if(typeof e==="number")e=e.toString();if(this.readyState!==WebSocket.OPEN){sendAfterClose(this,e,s);return}if(t===undefined)t=!this._isServer;this._sender.ping(e||g,t,s)}pong(e,t,s){if(this.readyState===WebSocket.CONNECTING){throw new Error("WebSocket is not open: readyState 0 (CONNECTING)")}if(typeof e==="function"){s=e;e=t=undefined}else if(typeof t==="function"){s=t;t=undefined}if(typeof e==="number")e=e.toString();if(this.readyState!==WebSocket.OPEN){sendAfterClose(this,e,s);return}if(t===undefined)t=!this._isServer;this._sender.pong(e||g,t,s)}resume(){if(this.readyState===WebSocket.CONNECTING||this.readyState===WebSocket.CLOSED){return}this._paused=false;if(!this._receiver._writableState.needDrain)this._socket.resume()}send(e,t,s){if(this.readyState===WebSocket.CONNECTING){throw new Error("WebSocket is not open: readyState 0 (CONNECTING)")}if(typeof t==="function"){s=t;t={}}if(typeof e==="number")e=e.toString();if(this.readyState!==WebSocket.OPEN){sendAfterClose(this,e,s);return}const r={binary:typeof e!=="string",mask:!this._isServer,compress:true,fin:true,...t};if(!this._extensions[h.extensionName]){r.compress=false}this._sender.send(e||g,r,s)}terminate(){if(this.readyState===WebSocket.CLOSED)return;if(this.readyState===WebSocket.CONNECTING){const e="WebSocket was closed before the connection was established";abortHandshake(this,this._req,e);return}if(this._socket){this._readyState=WebSocket.CLOSING;this._socket.destroy()}}}Object.defineProperty(WebSocket,"CONNECTING",{enumerable:true,value:D.indexOf("CONNECTING")});Object.defineProperty(WebSocket.prototype,"CONNECTING",{enumerable:true,value:D.indexOf("CONNECTING")});Object.defineProperty(WebSocket,"OPEN",{enumerable:true,value:D.indexOf("OPEN")});Object.defineProperty(WebSocket.prototype,"OPEN",{enumerable:true,value:D.indexOf("OPEN")});Object.defineProperty(WebSocket,"CLOSING",{enumerable:true,value:D.indexOf("CLOSING")});Object.defineProperty(WebSocket.prototype,"CLOSING",{enumerable:true,value:D.indexOf("CLOSING")});Object.defineProperty(WebSocket,"CLOSED",{enumerable:true,value:D.indexOf("CLOSED")});Object.defineProperty(WebSocket.prototype,"CLOSED",{enumerable:true,value:D.indexOf("CLOSED")});["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach((e=>{Object.defineProperty(WebSocket.prototype,e,{enumerable:true})}));["open","error","close","message"].forEach((e=>{Object.defineProperty(WebSocket.prototype,`on${e}`,{enumerable:true,get(){for(const t of this.listeners(e)){if(t[v])return t[S]}return null},set(t){for(const t of this.listeners(e)){if(t[v]){this.removeListener(e,t);break}}if(typeof t!=="function")return;this.addEventListener(e,t,{[v]:true})}})}));WebSocket.prototype.addEventListener=E;WebSocket.prototype.removeEventListener=x;e.exports=WebSocket;function initAsClient(e,t,s,r){const i={allowSynchronousEvents:true,autoPong:true,protocolVersion:M[1],maxPayload:100*1024*1024,skipUTF8Validation:false,perMessageDeflate:true,followRedirects:false,maxRedirects:10,...r,socketPath:undefined,hostname:undefined,protocol:undefined,timeout:undefined,method:"GET",host:undefined,path:undefined,port:undefined};e._autoPong=i.autoPong;if(!M.includes(i.protocolVersion)){throw new RangeError(`Unsupported protocol version: ${i.protocolVersion} `+`(supported versions: ${M.join(", ")})`)}let a;if(t instanceof d){a=t}else{try{a=new d(t)}catch(e){throw new SyntaxError(`Invalid URL: ${t}`)}}if(a.protocol==="http:"){a.protocol="ws:"}else if(a.protocol==="https:"){a.protocol="wss:"}e._url=a.href;const u=a.protocol==="wss:";const f=a.protocol==="ws+unix:";let p;if(a.protocol!=="ws:"&&!u&&!f){p='The URL\'s protocol must be one of "ws:", "wss:", '+'"http:", "https", or "ws+unix:"'}else if(f&&!a.pathname){p="The URL's pathname is empty"}else if(a.hash){p="The URL contains a fragment identifier"}if(p){const t=new SyntaxError(p);if(e._redirects===0){throw t}else{emitErrorAndClose(e,t);return}}const _=u?443:80;const y=c(16).toString("base64");const b=u?n.request:o.request;const g=new Set;let v;i.createConnection=i.createConnection||(u?tlsConnect:netConnect);i.defaultPort=i.defaultPort||_;i.port=a.port||_;i.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname;i.headers={...i.headers,"Sec-WebSocket-Version":i.protocolVersion,"Sec-WebSocket-Key":y,Connection:"Upgrade",Upgrade:"websocket"};i.path=a.pathname+a.search;i.timeout=i.handshakeTimeout;if(i.perMessageDeflate){v=new h(i.perMessageDeflate!==true?i.perMessageDeflate:{},false,i.maxPayload);i.headers["Sec-WebSocket-Extensions"]=O({[h.extensionName]:v.offer()})}if(s.length){for(const e of s){if(typeof e!=="string"||!B.test(e)||g.has(e)){throw new SyntaxError("An invalid or duplicated subprotocol was specified")}g.add(e)}i.headers["Sec-WebSocket-Protocol"]=s.join(",")}if(i.origin){if(i.protocolVersion<13){i.headers["Sec-WebSocket-Origin"]=i.origin}else{i.headers.Origin=i.origin}}if(a.username||a.password){i.auth=`${a.username}:${a.password}`}if(f){const e=i.path.split(":");i.socketPath=e[0];i.path=e[1]}let S;if(i.followRedirects){if(e._redirects===0){e._originalIpc=f;e._originalSecure=u;e._originalHostOrSocketPath=f?i.socketPath:a.host;const t=r&&r.headers;r={...r,headers:{}};if(t){for(const[e,s]of Object.entries(t)){r.headers[e.toLowerCase()]=s}}}else if(e.listenerCount("redirect")===0){const t=f?e._originalIpc?i.socketPath===e._originalHostOrSocketPath:false:e._originalIpc?false:a.host===e._originalHostOrSocketPath;if(!t||e._originalSecure&&!u){delete i.headers.authorization;delete i.headers.cookie;if(!t)delete i.headers.host;i.auth=undefined}}if(i.auth&&!r.headers.authorization){r.headers.authorization="Basic "+Buffer.from(i.auth).toString("base64")}S=e._req=b(i);if(e._redirects){e.emit("redirect",e.url,S)}}else{S=e._req=b(i)}if(i.timeout){S.on("timeout",(()=>{abortHandshake(e,S,"Opening handshake has timed out")}))}S.on("error",(t=>{if(S===null||S[P])return;S=e._req=null;emitErrorAndClose(e,t)}));S.on("response",(n=>{const o=n.headers.location;const a=n.statusCode;if(o&&i.followRedirects&&a>=300&&a<400){if(++e._redirects>i.maxRedirects){abortHandshake(e,S,"Maximum redirects exceeded");return}S.abort();let n;try{n=new d(o,t)}catch(t){const s=new SyntaxError(`Invalid URL: ${o}`);emitErrorAndClose(e,s);return}initAsClient(e,n,s,r)}else if(!e.emit("unexpected-response",S,n)){abortHandshake(e,S,`Unexpected server response: ${n.statusCode}`)}}));S.on("upgrade",((t,s,r)=>{e.emit("upgrade",t);if(e.readyState!==WebSocket.CONNECTING)return;S=e._req=null;const n=t.headers.upgrade;if(n===undefined||n.toLowerCase()!=="websocket"){abortHandshake(e,s,"Invalid Upgrade header");return}const o=l("sha1").update(y+m).digest("base64");if(t.headers["sec-websocket-accept"]!==o){abortHandshake(e,s,"Invalid Sec-WebSocket-Accept header");return}const a=t.headers["sec-websocket-protocol"];let c;if(a!==undefined){if(!g.size){c="Server sent a subprotocol but none was requested"}else if(!g.has(a)){c="Server sent an invalid subprotocol"}}else if(g.size){c="Server sent no subprotocol"}if(c){abortHandshake(e,s,c);return}if(a)e._protocol=a;const u=t.headers["sec-websocket-extensions"];if(u!==undefined){if(!v){const t="Server sent a Sec-WebSocket-Extensions header but no extension "+"was requested";abortHandshake(e,s,t);return}let t;try{t=T(u)}catch(t){const r="Invalid Sec-WebSocket-Extensions header";abortHandshake(e,s,r);return}const r=Object.keys(t);if(r.length!==1||r[0]!==h.extensionName){const t="Server indicated an extension that was not requested";abortHandshake(e,s,t);return}try{v.accept(t[h.extensionName])}catch(t){const r="Invalid Sec-WebSocket-Extensions header";abortHandshake(e,s,r);return}e._extensions[h.extensionName]=v}e.setSocket(s,r,{allowSynchronousEvents:i.allowSynchronousEvents,generateMask:i.generateMask,maxPayload:i.maxPayload,skipUTF8Validation:i.skipUTF8Validation})}));if(i.finishRequest){i.finishRequest(S,e)}else{S.end()}}function emitErrorAndClose(e,t){e._readyState=WebSocket.CLOSING;e._errorEmitted=true;e.emit("error",t);e.emitClose()}function netConnect(e){e.path=e.socketPath;return i.connect(e)}function tlsConnect(e){e.path=undefined;if(!e.servername&&e.servername!==""){e.servername=i.isIP(e.host)?"":e.host}return a.connect(e)}function abortHandshake(e,t,s){e._readyState=WebSocket.CLOSING;const r=new Error(s);Error.captureStackTrace(r,abortHandshake);if(t.setHeader){t[P]=true;t.abort();if(t.socket&&!t.socket.destroyed){t.socket.destroy()}process.nextTick(emitErrorAndClose,e,r)}else{t.destroy(r);t.once("error",e.emit.bind(e,"error"));t.once("close",e.emitClose.bind(e))}}function sendAfterClose(e,t,s){if(t){const s=y(t)?t.size:L(t).length;if(e._socket)e._sender._bufferedBytes+=s;else e._bufferedAmount+=s}if(s){const t=new Error(`WebSocket is not open: readyState ${e.readyState} `+`(${D[e.readyState]})`);process.nextTick(s,t)}}function receiverOnConclude(e,t){const s=this[k];s._closeFrameReceived=true;s._closeMessage=t;s._closeCode=e;if(s._socket[k]===undefined)return;s._socket.removeListener("data",socketOnData);process.nextTick(resume,s._socket);if(e===1005)s.close();else s.close(e,t)}function receiverOnDrain(){const e=this[k];if(!e.isPaused)e._socket.resume()}function receiverOnError(e){const t=this[k];if(t._socket[k]!==undefined){t._socket.removeListener("data",socketOnData);process.nextTick(resume,t._socket);t.close(e[w])}if(!t._errorEmitted){t._errorEmitted=true;t.emit("error",e)}}function receiverOnFinish(){this[k].emitClose()}function receiverOnMessage(e,t){this[k].emit("message",e,t)}function receiverOnPing(e){const t=this[k];if(t._autoPong)t.pong(e,!this._isServer,C);t.emit("ping",e)}function receiverOnPong(e){this[k].emit("pong",e)}function resume(e){e.resume()}function senderOnError(e){const t=this[k];if(t.readyState===WebSocket.CLOSED)return;if(t.readyState===WebSocket.OPEN){t._readyState=WebSocket.CLOSING;setCloseTimer(t)}this._socket.end();if(!t._errorEmitted){t._errorEmitted=true;t.emit("error",e)}}function setCloseTimer(e){e._closeTimer=setTimeout(e._socket.destroy.bind(e._socket),R)}function socketOnClose(){const e=this[k];this.removeListener("close",socketOnClose);this.removeListener("data",socketOnData);this.removeListener("end",socketOnEnd);e._readyState=WebSocket.CLOSING;let t;if(!this._readableState.endEmitted&&!e._closeFrameReceived&&!e._receiver._writableState.errorEmitted&&(t=e._socket.read())!==null){e._receiver.write(t)}e._receiver.end();this[k]=undefined;clearTimeout(e._closeTimer);if(e._receiver._writableState.finished||e._receiver._writableState.errorEmitted){e.emitClose()}else{e._receiver.on("error",receiverOnFinish);e._receiver.on("finish",receiverOnFinish)}}function socketOnData(e){if(!this[k]._receiver.write(e)){this.pause()}}function socketOnEnd(){const e=this[k];e._readyState=WebSocket.CLOSING;e._receiver.end();this.end()}function socketOnError(){const e=this[k];this.removeListener("error",socketOnError);this.on("error",C);if(e){e._readyState=WebSocket.CLOSING;this.destroy()}}},6939:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.decodeMessage=void 0;const r=s(2773);const n=s(785);const o=new TextDecoder;const i=new Uint8Array(0);let a=i;let c=0;const decodeRequestMethod=e=>{switch(e){case 1:return"GET";case 2:return"HEAD";case 3:return"POST";case 4:return"PUT";case 5:return"PATCH";case 6:return"DELETE";case 7:return"OPTIONS"}};const readUint8=()=>{const e=a[c];c+=4;return e};const readSmi=()=>{const e=a[c]<<24|a[c+1]<<16|a[c+2]<<8|a[c+3];c+=4;return e};const readString=()=>{c+=(0,n.alignBytes)(c);let e=c;for(;e<a.byteLength&&a[e]!==0;e++);const t=o.decode(a.slice(c,e));c=e+1;return t};const readHeaders=()=>{let e=c+=(0,n.alignBytes)(c);let t=0;const s=new Headers;while(e<a.byteLength&&a[e]!==0){for(t=e;t<a.byteLength&&a[t]!==0;t++);const r=o.decode(a.slice(e,t));e=t+1;if(e<a.byteLength){for(t=e;t<a.byteLength&&a[t]!==0;t++);const n=o.decode(a.slice(e,t));s.set(r,n);e=t+1}}c=t+1;return s};const readRestString=()=>{c+=(0,n.alignBytes)(c);if(c>=a.byteLength){return null}else{const e=o.decode(a.slice(c));c=a.byteLength;return e}};const readRestBytes=()=>{c+=(0,n.alignBytes)(c);if(c>=a.byteLength){return null}else{const e=a.slice(c);c=a.byteLength;return e}};const readRequestMessage=e=>({type:r.MessageType.Request,id:e,method:decodeRequestMethod(readUint8()),hasContent:readUint8()!==0,url:readString(),headers:readHeaders()});const readRequestAbortMessage=e=>({type:r.MessageType.RequestAbort,id:e,errored:readUint8()!==0});const readRequestBodyChunkMessage=e=>({type:r.MessageType.RequestBodyChunk,id:e,end:readUint8()!==0,data:readRestBytes()});const readResponseMessage=e=>({type:r.MessageType.Response,id:e,status:readSmi(),hasContent:readUint8()!==0,headers:readHeaders()});const readResponseAbortMessage=e=>({type:r.MessageType.ResponseAbort,id:e,errored:readUint8()!==0});const readResponseBodyChunkMessage=e=>({type:r.MessageType.ResponseBodyChunk,id:e,end:readUint8()!==0,data:readRestBytes()});const readWebSocketConnectMessage=e=>({type:r.MessageType.WebSocketConnect,id:e,url:readString()});const readWebSocketMessageMessage=e=>({type:r.MessageType.WebSocketMessage,id:e,data:readUint8()!==0?readRestString():readRestBytes()});const readWebSocketCloseMessage=e=>({type:r.MessageType.WebSocketClose,id:e});const readMessage=()=>{const e=readUint8();const t=readSmi();switch(e){case r.MessageType.Request:return readRequestMessage(t);case r.MessageType.RequestAbort:return readRequestAbortMessage(t);case r.MessageType.RequestBodyChunk:return readRequestBodyChunkMessage(t);case r.MessageType.Response:return readResponseMessage(t);case r.MessageType.ResponseAbort:return readResponseAbortMessage(t);case r.MessageType.ResponseBodyChunk:return readResponseBodyChunkMessage(t);case r.MessageType.WebSocketConnect:return readWebSocketConnectMessage(t);case r.MessageType.WebSocketMessage:return readWebSocketMessageMessage(t);case r.MessageType.WebSocketClose:return readWebSocketCloseMessage(t);default:throw new TypeError(`Received unknown message type: ${e}`)}};const decodeMessage=e=>{c=0;a=e;const t=readMessage();a=i;return t};t.decodeMessage=decodeMessage},4991:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.encodeMessage=void 0;const r=s(2773);const n=s(785);const o=new TextEncoder;const i=new Uint8Array(0);const a="\0";const c=(1<<8)-1;const l=8;let u=i;let f=0;const encodeRequestMethod=e=>{switch(e){case"GET":return 1;case"HEAD":return 2;case"POST":return 3;case"PUT":return 4;case"PATCH":return 5;case"DELETE":return 6;case"OPTIONS":return 7;default:return 1}};const encodeString=e=>o.encode(e+a);const encodeRestString=e=>e.length?o.encode(e):i;const encodeHeaders=e=>{let t="";for(const[s,r]of e)t+=s+a+r+a;return o.encode((t||a)+a)};const writeUint8=e=>{u[f]=e;f+=4};const writeSmi=e=>{u[f]=e>>>24&c;u[f+1]=e>>>16&c;u[f+2]=e>>>8&c;u[f+3]=e&c;f+=4};const writeBytes=e=>{f+=(0,n.alignBytes)(f);u.set(e,f);f+=e.byteLength};const writeRest=e=>{if(e?.byteLength){f+=(0,n.alignBytes)(f);u.set(e,f);f+=e.byteLength}};const writeMessageBase=e=>{writeUint8(e.type);writeSmi(e.id)};const encodeRequestMessage=e=>{const t=encodeRequestMethod(e.method);const s=e.hasContent?1:0;const r=encodeString(e.url);const o=encodeHeaders(e.headers);let a=l;a+=8;a+=(0,n.alignBytes)(a);a+=r.byteLength;a+=(0,n.alignBytes)(a);a+=o.byteLength;u=new Uint8Array(a);f=0;writeMessageBase(e);writeUint8(t);writeUint8(s);writeBytes(r);writeBytes(o);const c=u;u=i;return c};const encodeRequestAbortMessage=e=>{const t=e.errored?1:0;u=new Uint8Array(l+4);f=0;writeMessageBase(e);writeUint8(t);const s=u;u=i;return s};const encodeRequestBodyChunkMessage=e=>{const t=e.end?1:0;let s=l;s+=4;if(e.data?.byteLength){s+=(0,n.alignBytes)(s);s+=e.data.byteLength}u=new Uint8Array(s);f=0;writeMessageBase(e);writeUint8(t);writeRest(e.data);const r=u;u=i;return r};const encodeResponseMessage=e=>{const t=e.status;const s=e.hasContent?1:0;const r=encodeHeaders(e.headers);let o=l;o+=8;o+=(0,n.alignBytes)(o);o+=r.byteLength;u=new Uint8Array(o);f=0;writeMessageBase(e);writeSmi(t);writeUint8(s);writeBytes(r);const a=u;u=i;return a};const encodeResponseAbortMessage=e=>{const t=e.errored?1:0;u=new Uint8Array(l+4);f=0;writeMessageBase(e);writeUint8(t);const s=u;u=i;return s};const encodeResponseBodyChunkMessage=e=>{const t=e.end?1:0;let s=l;s+=4;if(e.data?.byteLength){s+=(0,n.alignBytes)(s);s+=e.data.byteLength}u=new Uint8Array(s);f=0;writeMessageBase(e);writeUint8(t);writeRest(e.data);const r=u;u=i;return r};const encodeWebSocketConnectMessage=e=>{const t=encodeString(e.url);f=0;u=new Uint8Array(l+(0,n.alignBytes)(l)+t.byteLength);writeMessageBase(e);writeBytes(t);const s=u;u=i;return s};const encodeWebSocketMessageMessage=e=>{const t=typeof e.data==="string"?1:0;const s=typeof e.data==="string"?encodeRestString(e.data):e.data;let r=l;r+=4;if(s?.byteLength){r+=(0,n.alignBytes)(r);r+=s.byteLength}u=new Uint8Array(r);f=0;writeMessageBase(e);writeUint8(t);writeRest(s);const o=u;u=i;return o};const encodeWebSocketCloseMessage=e=>{f=0;u=new Uint8Array(l);writeMessageBase(e);const t=u;u=i;return t};const encodeMessage=e=>{switch(e.type){case r.MessageType.Request:return encodeRequestMessage(e);case r.MessageType.RequestAbort:return encodeRequestAbortMessage(e);case r.MessageType.RequestBodyChunk:return encodeRequestBodyChunkMessage(e);case r.MessageType.Response:return encodeResponseMessage(e);case r.MessageType.ResponseAbort:return encodeResponseAbortMessage(e);case r.MessageType.ResponseBodyChunk:return encodeResponseBodyChunkMessage(e);case r.MessageType.WebSocketConnect:return encodeWebSocketConnectMessage(e);case r.MessageType.WebSocketMessage:return encodeWebSocketMessageMessage(e);case r.MessageType.WebSocketClose:return encodeWebSocketCloseMessage(e)}};t.encodeMessage=encodeMessage},1780:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.nextRequestID=void 0;const s=2**32;const phash=(e,t)=>{let s=(t||5381)|0;s=(s<<5)+s+e;return s|0};const nextRequestID=e=>{if(e==null){const e=typeof crypto==="undefined"?(Math.random()*2-1)*s&s-1:crypto.getRandomValues(new Int32Array(1))[0];return phash(e)}else{const t=phash(Date.now()%s);return phash(t,e)}};t.nextRequestID=nextRequestID},9236:function(e,t,s){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,s,r){if(r===undefined)r=s;var n=Object.getOwnPropertyDescriptor(t,s);if(!n||("get"in n?!t.__esModule:n.writable||n.configurable)){n={enumerable:true,get:function(){return t[s]}}}Object.defineProperty(e,r,n)}:function(e,t,s,r){if(r===undefined)r=s;e[r]=t[s]});var n=this&&this.__exportStar||function(e,t){for(var s in e)if(s!=="default"&&!Object.prototype.hasOwnProperty.call(t,s))r(t,e,s)};Object.defineProperty(t,"__esModule",{value:true});t.nextRequestID=void 0;n(s(2773),t);n(s(4991),t);n(s(6939),t);n(s(2845),t);var o=s(1780);Object.defineProperty(t,"nextRequestID",{enumerable:true,get:function(){return o.nextRequestID}})},2845:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.bodyToChunks=bodyToChunks;const s=1021*1024;function createChunkStream(){return new TransformStream({transform(e,t){if(e.byteLength<s){t.enqueue(e)}else{for(let r=0;r<e.byteLength;r+=s){t.enqueue(e.slice(r,r+s))}}}})}async function*bodyToChunks(e,t){const s=e.pipeThrough(createChunkStream(),t).getReader();try{let e;do{yield e=await s.read()}while(!e.done)}catch(e){if(t?.signal?.aborted){return}else{throw e}}finally{s.releaseLock()}}},2773:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.MessageType=void 0;var s;(function(e){e[e["Request"]=1]="Request";e[e["RequestAbort"]=2]="RequestAbort";e[e["RequestBodyChunk"]=3]="RequestBodyChunk";e[e["Response"]=4]="Response";e[e["ResponseAbort"]=5]="ResponseAbort";e[e["ResponseBodyChunk"]=6]="ResponseBodyChunk";e[e["WebSocketConnect"]=7]="WebSocketConnect";e[e["WebSocketMessage"]=8]="WebSocketMessage";e[e["WebSocketClose"]=9]="WebSocketClose"})(s||(t.MessageType=s={}))},785:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.alignBytes=void 0;const s=8;const alignBytes=e=>(s-e%s)%s;t.alignBytes=alignBytes},5712:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.handleProxiedRequest=handleProxiedRequest;t.pushProxiedRequestBodyChunk=pushProxiedRequestBodyChunk;t.abortProxiedRequest=abortProxiedRequest;const r=s(9802);const n=s(9236);const o=s(4430);const i=(0,o.createDebug)("http");const a=new Map;const c=new Map;async function handleProxiedRequest(e,t,s){const o=new Headers(s.headers);o.delete("origin");o.set("accept-encoding","gzip, deflate, br");o.set("X-Forwarded-Proto",t.protocol.replace(/:$/,""));o.set("X-Forwarded-Host",t.hostname);i("Executing proxied request with pending request body stream");const l=new AbortController;const u=l.signal;c.set(s.id,l);const f=s.hasContent?new ReadableStream({start(e){a.set(s.id,e)}}):null;let d;try{d=await(0,r.fetch)(s.url,{signal:l.signal,method:s.method,headers:o,body:f})}catch{a.delete(s.id);if(!u.aborted){e.send((0,n.encodeMessage)({type:n.MessageType.ResponseAbort,id:s.id,errored:true}))}return}try{for await(const t of encodeResponse(s.id,d,u))e.send(t)}finally{c.delete(s.id);a.delete(s.id)}}async function pushProxiedRequestBodyChunk(e){i("Streaming proxied request body chunk");const t=a.get(e.id);if(t){if(e.data)t.enqueue(e.data);if(e.end){t.close();a.delete(e.id)}}else{i("No active request stream found for message ID",e.id)}}async function abortProxiedRequest(e){i("Aborting proxied request");const t=c.get(e.id);if(t){t.abort(e.errored?new Error("Remote closed request stream"):undefined);c.delete(e.id)}const s=a.get(e.id);if(s&&e.errored){s.error(new Error("Remote closed request stream"))}else if(s){s.close()}if(!s&&!t){i("No active request to abort found for message ID",e.id)}}async function*encodeResponse(e,t,s){if(s.aborted)return;yield(0,n.encodeMessage)({type:n.MessageType.Response,id:e,hasContent:!!t.body,status:t.status,headers:t.headers});if(s.aborted){yield(0,n.encodeMessage)({type:n.MessageType.ResponseAbort,id:e,errored:false})}else if(t.body){try{for await(const r of(0,n.bodyToChunks)(t.body,{signal:s})){if(s.aborted)break;yield(0,n.encodeMessage)({type:n.MessageType.ResponseBodyChunk,id:e,end:r.done,data:r.value||null})}}catch(t){if(!s.aborted){i("Broken response body stream for request ID",e,t);yield(0,n.encodeMessage)({type:n.MessageType.ResponseAbort,id:e,errored:true})}}finally{if(s.aborted){yield(0,n.encodeMessage)({type:n.MessageType.RequestAbort,id:e,errored:false})}}}}},4450:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.createTunnel=createTunnel;const r=s(9236);const n=s(5712);const o=s(4917);const i=s(2244);const a=s(4430);const c=s(7104);const l=s(5096);const u=s(363);const f=(0,a.createDebug)("tunnel");function createTunnel(){let e=null;let t="created";let s=0;return{async start({apiUrl:r="wss://boltexpo.dev",session:n,maxReconnect:o=10,onStatusChange:i=()=>{}}){if(e&&e.readyState===e.OPEN){return(0,l.createTunnelUrl)(e.url).href}const{promise:a,resolve:d,reject:h}=(0,c.withResolvers)();const onSocketClose=()=>{if(t!=="started")return;if(s>o){f("Tunnel exceeded maximum reconnect attepts:",o);return i("disconnected")}f("Tunnel is reconnecting, attempt",s,"of",o);i("reconnecting");s+=1;this.stop();this.start({apiUrl:r,session:n,maxReconnect:o,onStatusChange:i})};try{i("connecting");e=(0,u.createWebSocket)((0,l.createSocketUrl)(r,n));e.addEventListener("error",h);e.addEventListener("open",d);e.addEventListener("close",onSocketClose);const t=(0,l.createTunnelUrl)(e.url);e.on("message",(s=>{try{if(e)handleTunnelMessage(e,t,s)}catch(e){f(e)}}));await a;i("connected");return t.href}catch(e){this.stop();throw e}finally{e?.removeEventListener("error",h);e?.addEventListener("error",(()=>{}))}},stop(){t="stopped";e?.close();e=null}}}function handleTunnelMessage(e,t,s){const a=(0,r.decodeMessage)((0,i.toUint8Array)(s));if(a.type===r.MessageType.Request){return(0,n.handleProxiedRequest)(e,t,a)}else if(a.type===r.MessageType.RequestAbort){return(0,n.abortProxiedRequest)(a)}else if(a.type===r.MessageType.RequestBodyChunk){return(0,n.pushProxiedRequestBodyChunk)(a)}else if(a.type===r.MessageType.WebSocketConnect){return(0,o.createProxiedWebsocket)(e,a)}else if(a.type===r.MessageType.WebSocketMessage){return(0,o.sendToProxiedWebsocket)(a)}else if(a.type===r.MessageType.WebSocketClose){return(0,o.closeProxiedWebsocket)(a)}else{f("Received unexpected message type",a);return Promise.reject(new Error(`Received unexpected message type: ${a.type}`))}}},4917:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.sendToProxiedWebsocket=sendToProxiedWebsocket;t.createProxiedWebsocket=createProxiedWebsocket;t.closeProxiedWebsocket=closeProxiedWebsocket;const r=s(9236);const n=s(2244);const o=s(4430);const i=s(363);const a=(0,o.createDebug)("websocket");const c=new Map;const l=new Map;async function sendToProxiedWebsocket(e){const t=c.get(e.id);if(t&&t.readyState===t.OPEN){t.send(e.data)}else if(t&&t.readyState===t.CONNECTING){let t=l.get(e.id);if(!t)l.set(e.id,t=[]);t.push(e)}else if(t){a("Proxied socket ID",e.id,"is in a bad state to forward messages to:",t.readyState)}else{a("Proxied socket ID",e.id,"requested, bt no socket is available")}}async function createProxiedWebsocket(e,t){let s=c.get(t.id);if(s)closeSocket(t.id,s);s=(0,i.createWebSocket)(t.url);c.set(t.id,s);s.addEventListener("close",(()=>{a("Proxied websocket closed");e.send((0,r.encodeMessage)({type:r.MessageType.WebSocketClose,id:t.id}));if(c.get(t.id)===s)c.delete(t.id)}));s.addEventListener("error",(e=>{a("Proxied websocket threw an error",e.error)}));s.on("message",((s,o)=>{a("Proxied websocket emitted message for websocket ID",t.id);if(o){const o=(0,n.toUint8Array)(s);e.send((0,r.encodeMessage)({type:r.MessageType.WebSocketMessage,id:t.id,data:o}))}else{const o=(0,n.toString)(s);e.send((0,r.encodeMessage)({type:r.MessageType.WebSocketMessage,id:t.id,data:o}))}}));s.on("open",(()=>{a("Proxied websocket opened to",t.url,"for websocket ID",t.id);l.get(t.id)?.forEach((e=>e.data&&s.send(e.data)));l.delete(t.id)}))}function closeSocket(e,t){t.close();c.delete(e);l.delete(e)}async function closeProxiedWebsocket(e){const t=c.get(e.id);if(t){a("Closing proxied websocket ID",e.id);closeSocket(e.id,t)}else{a("Close of socket ID",e.id,"requested, but no socket is available")}}},2244:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.toUint8Array=toUint8Array;t.toString=toString;const r=s(4430);const n=(0,r.createDebug)("tunnel");const o=new TextDecoder;function toUint8Array(e){if(Buffer.isBuffer(e)){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}else if(e instanceof ArrayBuffer){return new Uint8Array(e)}else if(Array.isArray(e)){return new Uint8Array(Buffer.concat(e))}else{n("Invalid tunnel ws message received",e);throw new TypeError("Invalid tunnel ws message received")}}function toString(e){if(typeof e==="string"){return e}else if(Buffer.isBuffer(e)){return e.toString()}else if(e instanceof ArrayBuffer){return o.decode(e)}else if(Array.isArray(e)){return o.decode(Buffer.concat(e))}else{n("Invalid proxy ws message received",e);throw new TypeError("Invalid proxy ws message received")}}},4430:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.createDebug=createDebug;const n=r(s(2830));function createDebug(e){return(0,n.default)("tunnel").extend(e)}},7104:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.withResolvers=withResolvers;function withResolvers(){if(typeof Promise.withResolvers==="function"){return Promise.withResolvers()}let e;let t;const s=new Promise(((s,r)=>{e=s;t=r}));return{resolve:e,reject:t,promise:s}}},5096:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.createSocketUrl=createSocketUrl;t.createTunnelUrl=createTunnelUrl;function createSocketUrl(e,t){const s=new URL(e);s.searchParams.set("_tunnel","true");if(t){s.hostname=`${santizeSubdomainCharacters(t)}.${s.hostname}`}return s}function createTunnelUrl(e){const t=new URL(e);t.searchParams.delete("_tunnel");if(t.protocol==="wss:")t.protocol="https:";if(t.protocol==="ws:")t.protocol="http:";return t}function santizeSubdomainCharacters(e){return e.replace(/^[^A-Za-z0-9]|[^A-Za-z0-9]$/g,"a")}},363:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.createWebSocket=createWebSocket;const r=s(1354);function createWebSocket(e){const t=new r.WebSocket(e,{skipUTF8Validation:true,maxPayload:1024*1024,perMessageDeflate:false});t.binaryType="arraybuffer";return t}},8327:module=>{module.exports=eval("require")("bufferutil")},2414:module=>{module.exports=eval("require")("utf-8-validate")},181:e=>{"use strict";e.exports=require("buffer")},6982:e=>{"use strict";e.exports=require("crypto")},4434:e=>{"use strict";e.exports=require("events")},8611:e=>{"use strict";e.exports=require("http")},5692:e=>{"use strict";e.exports=require("https")},9278:e=>{"use strict";e.exports=require("net")},4573:e=>{"use strict";e.exports=require("node:buffer")},7598:e=>{"use strict";e.exports=require("node:crypto")},7067:e=>{"use strict";e.exports=require("node:http")},4708:e=>{"use strict";e.exports=require("node:https")},7075:e=>{"use strict";e.exports=require("node:stream")},3136:e=>{"use strict";e.exports=require("node:url")},3429:e=>{"use strict";e.exports=require("node:util/types")},8522:e=>{"use strict";e.exports=require("node:zlib")},857:e=>{"use strict";e.exports=require("os")},2203:e=>{"use strict";e.exports=require("stream")},4756:e=>{"use strict";e.exports=require("tls")},2018:e=>{"use strict";e.exports=require("tty")},7016:e=>{"use strict";e.exports=require("url")},9023:e=>{"use strict";e.exports=require("util")},3106:e=>{"use strict";e.exports=require("zlib")}};var __webpack_module_cache__={};function __nccwpck_require__(e){var t=__webpack_module_cache__[e];if(t!==undefined){return t.exports}var s=__webpack_module_cache__[e]={exports:{}};var r=true;try{__webpack_modules__[e].call(s.exports,s,s.exports,__nccwpck_require__);r=false}finally{if(r)delete __webpack_module_cache__[e]}return s.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var __webpack_exports__={};(()=>{"use strict";var e=__webpack_exports__;Object.defineProperty(e,"__esModule",{value:true});e.startAsync=startAsync;e.stopAsync=stopAsync;const t=__nccwpck_require__(4450);const s=(0,t.createTunnel)();async function startAsync(e){s.stop();return await s.start(e)}async function stopAsync(){s.stop()}})();module.exports=__webpack_exports__})();