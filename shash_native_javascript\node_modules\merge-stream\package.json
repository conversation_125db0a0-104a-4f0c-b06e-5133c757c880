{"name": "merge-stream", "version": "2.0.0", "description": "Create a stream that emits events from multiple other streams", "files": ["index.js"], "scripts": {"test": "istanbul cover test.js && istanbul check-cover --statements 100 --branches 100"}, "repository": "grncdr/merge-stream", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {}, "devDependencies": {"from2": "^2.0.3", "istanbul": "^0.4.5"}}