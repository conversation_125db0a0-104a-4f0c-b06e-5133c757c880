{"name": "p-try", "version": "2.2.0", "description": "`Start a promise chain", "license": "MIT", "repository": "sindresorhus/p-try", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "try", "resolve", "function", "catch", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}}